<template>
  <div class="business-hours-test">
    <div class="header">
      <button class="back-btn" @click="goBack">←</button>
      <h1>营业时间测试</h1>
    </div>

    <div class="test-content">
      <div class="current-time">
        <h3>当前时间</h3>
        <p>{{ currentTime }}</p>
      </div>

      <div class="test-cases">
        <h3>测试案例</h3>
        <div v-for="testCase in testCases" :key="testCase.name" class="test-case">
          <div class="case-info">
            <h4>{{ testCase.name }}</h4>
            <p>营业时间: {{ testCase.startTime }} - {{ testCase.endTime }}</p>
          </div>
          <div class="case-result">
            <span class="status" :class="testCase.result.className">
              {{ testCase.result.text }}
            </span>
          </div>
        </div>
      </div>

      <div class="time-simulator">
        <h3>时间模拟器</h3>
        <div class="simulator-controls">
          <label>
            模拟时间:
            <input 
              v-model="simulatedTime" 
              type="time" 
              @change="updateSimulation"
            >
          </label>
          <button @click="resetToCurrentTime">重置为当前时间</button>
        </div>
        
        <div class="simulation-results">
          <div v-for="testCase in testCases" :key="testCase.name" class="sim-result">
            <span class="case-name">{{ testCase.name }}:</span>
            <span class="status" :class="testCase.simulatedResult.className">
              {{ testCase.simulatedResult.text }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getBusinessStatus } from '@/utils/businessHours'

export default {
  name: 'BusinessHoursTest',
  setup() {
    const router = useRouter()
    const currentTime = ref('')
    const simulatedTime = ref('')
    const testCases = ref([])
    
    let timeInterval = null

    const goBack = () => {
      router.go(-1)
    }

    const updateCurrentTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const createTestCases = () => {
      const cases = [
        { name: '全天营业', startTime: '00:00', endTime: '23:59' },
        { name: '正常营业', startTime: '09:00', endTime: '22:00' },
        { name: '早餐店', startTime: '06:00', endTime: '11:00' },
        { name: '夜宵店', startTime: '20:00', endTime: '02:00' },
        { name: '午餐店', startTime: '11:00', endTime: '14:00' },
        { name: '咖啡店', startTime: '07:00', endTime: '21:00' }
      ]

      testCases.value = cases.map(testCase => ({
        ...testCase,
        result: getBusinessStatus(testCase.startTime, testCase.endTime),
        simulatedResult: getBusinessStatus(testCase.startTime, testCase.endTime)
      }))
    }

    const updateSimulation = () => {
      if (!simulatedTime.value) return

      // 创建模拟的日期时间
      const [hours, minutes] = simulatedTime.value.split(':').map(Number)
      const simulatedDate = new Date()
      simulatedDate.setHours(hours, minutes, 0, 0)

      testCases.value.forEach(testCase => {
        testCase.simulatedResult = getBusinessStatus(
          testCase.startTime, 
          testCase.endTime, 
          simulatedDate
        )
      })
    }

    const resetToCurrentTime = () => {
      const now = new Date()
      simulatedTime.value = now.toTimeString().slice(0, 5)
      updateSimulation()
    }

    onMounted(() => {
      updateCurrentTime()
      createTestCases()
      resetToCurrentTime()
      
      // 每秒更新当前时间
      timeInterval = setInterval(() => {
        updateCurrentTime()
        createTestCases() // 重新计算实时状态
      }, 1000)
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      simulatedTime,
      testCases,
      goBack,
      updateSimulation,
      resetToCurrentTime
    }
  }
}
</script>

<style scoped>
.business-hours-test {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 20px;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  margin-right: 12px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.test-content {
  padding: 16px;
}

.current-time {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.current-time h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.current-time p {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #007AFF;
}

.test-cases {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.test-cases h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.test-case {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.test-case:last-child {
  border-bottom: none;
}

.case-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
}

.case-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.open {
  background: #e8f5e8;
  color: #4caf50;
}

.status.closed {
  background: #ffeaea;
  color: #f44336;
}

.time-simulator {
  background: white;
  padding: 16px;
  border-radius: 8px;
}

.time-simulator h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.simulator-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.simulator-controls label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.simulator-controls input {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.simulator-controls button {
  background: #007AFF;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.simulation-results {
  display: grid;
  gap: 8px;
}

.sim-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.case-name {
  font-size: 14px;
}
</style>
