<template>
  <div class="order-api-test">
    <div class="header">
      <h1>📋 订单API测试</h1>
      <p>测试订单相关API接口</p>
    </div>

    <!-- API说明 -->
    <div class="user-section">
      <h2>ℹ️ API说明</h2>
      <div class="api-info">
        <div class="info-item">
          <h3>📋 订单创建</h3>
          <p>根据API文档，customer_name和customer_phone是必填字段，使用默认值</p>
        </div>
        <div class="info-item">
          <h3>🔧 字段映射</h3>
          <p>table_number → table_no, specialRequirements → remark</p>
        </div>
        <div class="info-note">
          <small>💡 提示：API字段已根据官方文档进行调整</small>
        </div>
      </div>
    </div>

    <!-- 测试按钮区域 -->
    <div class="test-section">
      <h2>🧪 API测试</h2>
      
      <div class="test-buttons">
        <button @click="testCreateOrder" :disabled="loading">创建测试订单</button>
        <button @click="testGetUserOrders" :disabled="loading">获取用户订单</button>
        <button @click="testGetOrderDetail" :disabled="loading">获取订单详情</button>
        <button @click="testCancelOrder" :disabled="loading">取消订单</button>
        <button @click="testOrderStatus" :disabled="loading">获取订单状态</button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <SvgIcon name="loading" size="24px" color="#007AFF" />
        <span>请求中...</span>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error">
        <h3>❌ 错误信息：</h3>
        <pre>{{ error }}</pre>
      </div>

      <!-- 成功结果 -->
      <div v-if="result" class="result">
        <h3>✅ API响应结果：</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </div>

    <!-- 订单状态监控 -->
    <div class="monitoring-section" v-if="monitoringOrders.length > 0">
      <h2>📊 订单状态监控</h2>
      <div class="monitoring-list">
        <div v-for="order in monitoringOrders" :key="order.id" class="monitoring-item">
          <div class="order-info">
            <span class="order-id">订单 #{{ order.id }}</span>
            <span class="order-status" :class="order.status">{{ order.statusText }}</span>
          </div>
          <div class="order-actions">
            <button @click="stopMonitoring(order.id)" class="stop-btn">停止监控</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试数据模板 -->
    <div class="template-section">
      <h2>📝 测试数据模板</h2>
      <div class="template-content">
        <h3>创建订单数据:</h3>
        <pre class="template-code">{{ JSON.stringify(orderTemplate, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useOrdersStore } from '@/stores/orders'
import apiService from '@/services/api'
import { getSmartTimeDisplay } from '@/utils/dateUtils'
import SvgIcon from '@/components/SvgIcon.vue'

export default {
  name: 'OrderApiTest',
  components: {
    SvgIcon
  },
  setup() {
    const ordersStore = useOrdersStore()
    const loading = ref(false)
    const error = ref(null)
    const result = ref(null)
    const monitoringOrders = ref([])
    const lastCreatedOrderId = ref(null)

    // 测试订单模板（根据API文档调整）
    const orderTemplate = {
      customer_name: '测试用户',
      customer_phone: '13800000000',
      shop_code: 'SHOP_001', // 店铺编号
      items: [
        {
          dish_id: 1,
          quantity: 2,
          special_requirements: '不要辣'
        },
        {
          dish_id: 2,
          quantity: 1,
          special_requirements: ''
        }
      ],
      dining_type: 'dine_in',
      table_no: 'A01',
      remark: '请尽快制作'
    }



    // 测试创建订单
    const testCreateOrder = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        // 直接使用订单模板，不包含用户信息
        const testData = { ...orderTemplate }

        console.log('🚀 创建测试订单:', testData)
        const orderResult = await apiService.createOrder(testData)
        result.value = formatApiResponse(orderResult)
        lastCreatedOrderId.value = orderResult.id

        // 开始监控这个订单
        startMonitoring(orderResult.id)

        console.log('✅ 订单创建成功:', orderResult)
      } catch (err) {
        error.value = err.message
        console.error('❌ 创建订单失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 测试获取用户订单
    const testGetUserOrders = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        // 提示用户输入手机号查询订单
        const customerPhone = prompt('请输入手机号查询订单:')
        if (!customerPhone) {
          alert('需要手机号才能查询订单')
          return
        }

        const orders = await apiService.getUserOrders(customerPhone)
        result.value = formatApiResponse(orders)
        console.log('✅ 获取用户订单成功:', orders)
      } catch (err) {
        error.value = err.message
        console.error('❌ 获取用户订单失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 测试获取订单详情
    const testGetOrderDetail = async () => {
      const orderId = lastCreatedOrderId.value || prompt('请输入订单ID:')
      if (!orderId) return

      loading.value = true
      error.value = null
      result.value = null

      try {
        const order = await apiService.getOrderDetail(orderId)
        result.value = formatApiResponse(order)
        console.log('✅ 获取订单详情成功:', order)
      } catch (err) {
        error.value = err.message
        console.error('❌ 获取订单详情失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 测试取消订单
    const testCancelOrder = async () => {
      const orderId = lastCreatedOrderId.value || prompt('请输入要取消的订单ID:')
      if (!orderId) return

      loading.value = true
      error.value = null
      result.value = null

      try {
        const cancelResult = await apiService.cancelOrder(orderId, '测试取消')
        result.value = cancelResult
        console.log('✅ 取消订单成功:', cancelResult)
      } catch (err) {
        error.value = err.message
        console.error('❌ 取消订单失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 测试获取订单状态
    const testOrderStatus = async () => {
      const orderId = lastCreatedOrderId.value || prompt('请输入订单ID:')
      if (!orderId) return

      loading.value = true
      error.value = null
      result.value = null

      try {
        const status = await apiService.getOrderStatus(orderId)
        result.value = formatApiResponse(status)
        console.log('✅ 获取订单状态成功:', status)
      } catch (err) {
        error.value = err.message
        console.error('❌ 获取订单状态失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 开始监控订单
    const startMonitoring = (orderId) => {
      const existingOrder = monitoringOrders.value.find(o => o.id === orderId)
      if (existingOrder) return

      const orderMonitor = {
        id: orderId,
        status: 'pending',
        statusText: '等待确认',
        interval: null
      }

      // 开始轮询
      orderMonitor.interval = setInterval(async () => {
        try {
          const status = await apiService.getOrderStatus(orderId)
          orderMonitor.status = status.status
          orderMonitor.statusText = getStatusText(status.status)
          
          console.log(`📊 订单 ${orderId} 状态更新:`, status)
          
          // 如果订单完成，停止监控
          if (['completed', 'cancelled', 'refunded'].includes(status.status)) {
            stopMonitoring(orderId)
          }
        } catch (error) {
          console.error(`❌ 监控订单 ${orderId} 失败:`, error)
        }
      }, 10000) // 每10秒检查一次

      monitoringOrders.value.push(orderMonitor)
      console.log(`🔄 开始监控订单 ${orderId}`)
    }

    // 停止监控订单
    const stopMonitoring = (orderId) => {
      const orderIndex = monitoringOrders.value.findIndex(o => o.id === orderId)
      if (orderIndex > -1) {
        const order = monitoringOrders.value[orderIndex]
        if (order.interval) {
          clearInterval(order.interval)
        }
        monitoringOrders.value.splice(orderIndex, 1)
        console.log(`⏹️ 停止监控订单 ${orderId}`)
      }
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'pending': '等待确认',
        'confirmed': '已确认',
        'preparing': '制作中',
        'ready': '已出餐',
        'serving': '配送中',
        'completed': '已完成',
        'cancelled': '已取消',
        'refunded': '已退款'
      }
      return statusMap[status] || status
    }

    // 格式化API响应显示
    const formatApiResponse = (data) => {
      if (!data) return data

      // 如果是订单数据，格式化时间字段
      if (data.order_time) {
        return {
          ...data,
          order_time_formatted: getSmartTimeDisplay(data.order_time),
          order_time_raw: data.order_time
        }
      }

      // 如果是订单列表
      if (data.list && Array.isArray(data.list)) {
        return {
          ...data,
          list: data.list.map(order => ({
            ...order,
            order_time_formatted: order.order_time ? getSmartTimeDisplay(order.order_time) : '--'
          }))
        }
      }

      return data
    }

    // 页面加载时初始化
    onMounted(() => {
      ordersStore.init()
    })

    // 页面卸载时清理监控
    onUnmounted(() => {
      monitoringOrders.value.forEach(order => {
        if (order.interval) {
          clearInterval(order.interval)
        }
      })
    })

    return {
      ordersStore,
      loading,
      error,
      result,
      monitoringOrders,
      orderTemplate,
      testCreateOrder,
      testGetUserOrders,
      testGetOrderDetail,
      testCancelOrder,
      testOrderStatus,
      stopMonitoring,
      formatApiResponse
    }
  }
}
</script>

<style scoped>
.order-api-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
}

.user-section, .test-section, .monitoring-section, .template-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.api-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.info-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.info-note {
  margin-top: 10px;
  padding: 8px 12px;
  background: #e3f2fd;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.info-note small {
  color: #1976d2;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 10px 16px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s;
}

.test-buttons button:hover:not(:disabled) {
  background: #0056b3;
}

.test-buttons button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #007AFF;
  margin: 20px 0;
}

.error {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.error h3 {
  color: #f44336;
  margin: 0 0 10px 0;
}

.error pre {
  color: #d32f2f;
  white-space: pre-wrap;
  word-break: break-word;
}

.result {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.result h3 {
  color: #4caf50;
  margin: 0 0 10px 0;
}

.result pre {
  color: #2e7d32;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.monitoring-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.monitoring-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #f8f9fa;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.order-id {
  font-weight: 500;
}

.order-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.confirmed {
  background: #d1ecf1;
  color: #0c5460;
}

.order-status.preparing {
  background: #e2e3e5;
  color: #383d41;
}

.order-status.ready {
  background: #d4edda;
  color: #155724;
}

.order-status.completed {
  background: #d4edda;
  color: #155724;
}

.order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.stop-btn {
  padding: 6px 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.template-content {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}

.template-code {
  color: #495057;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
