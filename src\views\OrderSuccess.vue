<template>
  <div class="order-success">
    <!-- 顶部导航 -->
    <div class="header">
      <h1>点餐成功</h1>
    </div>

    <!-- 成功提示 -->
    <div class="success-section">
      <div class="success-icon">
        <SvgIcon name="success" size="64px" color="#4caf50" />
      </div>
      <h2>点餐成功！</h2>
      <p>您的订单已成功提交</p>
      <div class="order-number">
        <span class="label">订单号：</span>
        <span class="number">{{ orderNumber }}</span>
      </div>
    </div>

    <!-- 出单号状态 -->
    <div class="meal-number-section">
      <h3>出餐号分配</h3>
      <div class="meal-numbers-container">
        <div 
          v-for="(item, index) in orderItems" 
          :key="item.id"
          class="meal-item"
        >
          <div class="item-info">
            <span class="item-name">{{ item.name }}</span>
          </div>
          <div class="number-status">
            <div v-if="item.mealNumber" class="meal-number assigned">
              {{ item.mealNumber }}
            </div>
            <div v-else class="meal-number pending">
              <span class="loading-dots">分配中</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情 -->
    <div class="order-details">
      <h3>订单详情</h3>
      <div class="restaurant-info">
        <div class="restaurant-name">{{ restaurant.name }}</div>
        <div class="restaurant-address">{{ restaurant.address }}</div>
      </div>
      <div class="order-summary">
        <div class="summary-item">
          <span>菜品数量：</span>
          <span>{{ orderItems.length }} 道</span>
        </div>
        <div class="summary-item">
          <span>下单时间：</span>
          <span>{{ orderTime }}</span>
        </div>
      </div>
    </div>

    <!-- 温馨提示 -->
    <div class="tips-section">
      <h3>温馨提示</h3>
      <ul>
        <li>出餐号正在分配中，请稍候</li>
        <li>每道菜品都有独立的出餐号</li>
        <li>分配完成后将自动跳转到订单状态页面</li>
        <li>请留意大屏幕或听取广播叫号</li>
      </ul>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <button class="action-btn secondary" @click="goToMenu">继续点餐</button>
      <button class="action-btn primary" @click="goToOrders">查看订单</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'
import SvgIcon from '@/components/SvgIcon.vue'

export default {
  name: 'OrderSuccess',
  components: {
    SvgIcon
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const ordersStore = useOrdersStore()
    
    const orderNumber = route.params.orderNumber
    const restaurant = ref({})
    const orderItems = ref([])
    const orderTime = ref('')
    
    let assignmentInterval = null
    let allAssigned = false

    const goToMenu = () => {
      router.push(`/menu/${restaurant.value.id}`)
    }

    const goToOrders = () => {
      router.push('/orders')
    }

    const generateMealNumber = () => {
      return String(Math.floor(Math.random() * 900) + 100)
    }

    const assignMealNumbers = () => {
      if (allAssigned) return

      // 随机选择一个还没有出餐号的菜品
      const pendingItems = orderItems.value.filter(item => !item.mealNumber)
      
      if (pendingItems.length === 0) {
        allAssigned = true
        clearInterval(assignmentInterval)
        
        // 更新订单状态
        ordersStore.updateOrderItems(orderNumber, orderItems.value.map(item => ({
          ...item,
          status: 'cooking' // 开始制作
        })))
        
        // 等待2秒后跳转到订单状态页面
        setTimeout(() => {
          router.replace({
            name: 'OrderStatus',
            params: { orderNumber }
          })
        }, 2000)
        
        return
      }

      // 随机选择一个菜品分配出餐号
      const randomIndex = Math.floor(Math.random() * pendingItems.length)
      const selectedItem = pendingItems[randomIndex]
      
      // 在原数组中找到对应项目并分配出餐号
      const itemIndex = orderItems.value.findIndex(item => item.id === selectedItem.id)
      if (itemIndex > -1) {
        orderItems.value[itemIndex].mealNumber = generateMealNumber()
      }
    }

    onMounted(() => {
      // 初始化订单管理
      ordersStore.init()
      
      // 获取订单信息
      const order = ordersStore.getOrder(orderNumber)
      if (order) {
        restaurant.value = order.restaurant
        orderItems.value = [...order.items] // 创建副本以便修改
        orderTime.value = new Date(order.orderTime).toLocaleString()
      }

      // 开始分配出餐号，每2-4秒分配一个
      assignmentInterval = setInterval(() => {
        assignMealNumbers()
      }, Math.random() * 2000 + 2000) // 2-4秒随机间隔
    })

    onUnmounted(() => {
      if (assignmentInterval) {
        clearInterval(assignmentInterval)
      }
    })

    return {
      orderNumber,
      restaurant,
      orderItems,
      orderTime,
      goToMenu,
      goToOrders
    }
  }
}
</script>

<style scoped>
.order-success {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.header {
  text-align: center;
  padding: 20px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.success-section {
  background: white;
  padding: 40px 20px;
  margin: 16px;
  border-radius: 12px;
  text-align: center;
}

.success-icon {
  margin-bottom: 16px;
}

.success-section h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #4caf50;
}

.success-section p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

.order-number {
  background: #f0f8ff;
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #007AFF;
  display: inline-block;
}

.order-number .label {
  color: #666;
  font-size: 14px;
}

.order-number .number {
  color: #007AFF;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.meal-number-section {
  background: white;
  padding: 16px;
  margin: 16px;
  border-radius: 8px;
}

.meal-number-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.meal-numbers-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
}

.meal-number {
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.meal-number.assigned {
  background: #e8f5e8;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.meal-number.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.loading-dots {
  font-size: 12px;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

.order-details {
  background: white;
  padding: 16px;
  margin: 16px;
  border-radius: 8px;
}

.order-details h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.restaurant-info {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.restaurant-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.restaurant-address {
  font-size: 14px;
  color: #666;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.summary-item span:first-child {
  color: #666;
}

.tips-section {
  background: white;
  padding: 16px;
  margin: 16px;
  border-radius: 8px;
}

.tips-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.tips-section ul {
  margin: 0;
  padding-left: 20px;
}

.tips-section li {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
  border: none;
}

.action-btn:hover {
  transform: translateY(-1px);
}
</style>
