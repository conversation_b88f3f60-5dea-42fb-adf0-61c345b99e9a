<template>
  <div class="proxy-status" :class="statusClass">
    <div class="status-indicator">
      <SvgIcon :name="statusIcon" size="16px" :color="statusColor" />
      <span class="status-text">{{ statusText }}</span>
    </div>
    <div class="status-details" v-if="showDetails">
      <div class="detail-item">
        <span class="label">代理地址:</span>
        <span class="value">{{ proxyTarget }}</span>
      </div>
      <div class="detail-item">
        <span class="label">最后检查:</span>
        <span class="value">{{ lastCheck }}</span>
      </div>
      <div class="detail-item" v-if="error">
        <span class="label">错误信息:</span>
        <span class="value error">{{ error }}</span>
      </div>
    </div>
    <button @click="toggleDetails" class="toggle-btn">
      {{ showDetails ? '隐藏' : '详情' }}
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import SvgIcon from './SvgIcon.vue'
import apiService from '@/services/api'

export default {
  name: 'ProxyStatus',
  components: {
    SvgIcon
  },
  setup() {
    const status = ref('checking') // checking, connected, disconnected, error
    const error = ref(null)
    const lastCheck = ref('')
    const showDetails = ref(false)
    const proxyTarget = 'http://localhost:8000'
    
    let checkInterval = null

    // 计算状态相关属性
    const statusClass = computed(() => {
      return {
        'status-checking': status.value === 'checking',
        'status-connected': status.value === 'connected',
        'status-disconnected': status.value === 'disconnected',
        'status-error': status.value === 'error'
      }
    })

    const statusIcon = computed(() => {
      switch (status.value) {
        case 'checking': return 'loading'
        case 'connected': return 'success'
        case 'disconnected': return 'warning'
        case 'error': return 'warning'
        default: return 'warning'
      }
    })

    const statusColor = computed(() => {
      switch (status.value) {
        case 'checking': return '#007AFF'
        case 'connected': return '#28a745'
        case 'disconnected': return '#ffc107'
        case 'error': return '#dc3545'
        default: return '#6c757d'
      }
    })

    const statusText = computed(() => {
      switch (status.value) {
        case 'checking': return '检查代理连接...'
        case 'connected': return '代理连接正常'
        case 'disconnected': return '代理连接断开'
        case 'error': return '代理连接错误'
        default: return '未知状态'
      }
    })

    // 检查代理连接状态
    const checkProxyStatus = async () => {
      status.value = 'checking'
      error.value = null
      
      try {
        // 尝试调用一个简单的API来检查代理状态
        await apiService.getMenuCategories()
        status.value = 'connected'
        console.log('✅ 代理连接正常')
      } catch (err) {
        console.error('❌ 代理连接失败:', err.message)
        error.value = err.message
        
        if (err.message.includes('fetch')) {
          status.value = 'disconnected'
        } else {
          status.value = 'error'
        }
      }
      
      lastCheck.value = new Date().toLocaleTimeString()
    }

    // 切换详情显示
    const toggleDetails = () => {
      showDetails.value = !showDetails.value
    }

    // 组件挂载时开始检查
    onMounted(() => {
      checkProxyStatus()
      // 每30秒检查一次
      checkInterval = setInterval(checkProxyStatus, 30000)
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (checkInterval) {
        clearInterval(checkInterval)
      }
    })

    return {
      status,
      error,
      lastCheck,
      showDetails,
      proxyTarget,
      statusClass,
      statusIcon,
      statusColor,
      statusText,
      toggleDetails,
      checkProxyStatus
    }
  }
}
</script>

<style scoped>
.proxy-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  background: #f8f9fa;
  font-size: 12px;
  transition: all 0.2s;
}

.proxy-status.status-checking {
  border-color: #007AFF;
  background: #e3f2fd;
}

.proxy-status.status-connected {
  border-color: #28a745;
  background: #d4edda;
}

.proxy-status.status-disconnected {
  border-color: #ffc107;
  background: #fff3cd;
}

.proxy-status.status-error {
  border-color: #dc3545;
  background: #f8d7da;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-text {
  font-weight: 500;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 10px;
  padding-left: 10px;
  border-left: 1px solid #dee2e6;
}

.detail-item {
  display: flex;
  gap: 6px;
}

.detail-item .label {
  font-weight: 500;
  color: #6c757d;
}

.detail-item .value {
  color: #333;
}

.detail-item .value.error {
  color: #dc3545;
}

.toggle-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}
</style>
