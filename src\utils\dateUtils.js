/**
 * 日期时间工具函数
 */

/**
 * 格式化订单时间
 * @param {string|Date} dateTime - 日期时间
 * @param {string} format - 格式类型
 * @returns {string} 格式化后的时间字符串
 */
export function formatOrderTime(dateTime, format = 'full') {
  if (!dateTime) return '--'
  
  const date = new Date(dateTime)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('无效的日期时间:', dateTime)
    return '--'
  }
  
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  switch (format) {
    case 'relative':
      // 相对时间格式
      if (diffMinutes < 1) return '刚刚'
      if (diffMinutes < 60) return `${diffMinutes}分钟前`
      if (diffHours < 24) return `${diffHours}小时前`
      if (diffDays < 7) return `${diffDays}天前`
      return formatOrderTime(dateTime, 'date')
      
    case 'time':
      // 只显示时间
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
      
    case 'date':
      // 只显示日期
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
      
    case 'datetime':
      // 日期 + 时间
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
      
    case 'full':
    default:
      // 完整格式
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
  }
}

/**
 * 获取订单时间的显示文本
 * @param {string|Date} orderTime - 订单时间
 * @returns {object} 包含不同格式的时间文本
 */
export function getOrderTimeDisplay(orderTime) {
  return {
    full: formatOrderTime(orderTime, 'full'),
    datetime: formatOrderTime(orderTime, 'datetime'),
    relative: formatOrderTime(orderTime, 'relative'),
    date: formatOrderTime(orderTime, 'date'),
    time: formatOrderTime(orderTime, 'time')
  }
}

/**
 * 判断是否为今天的订单
 * @param {string|Date} orderTime - 订单时间
 * @returns {boolean}
 */
export function isToday(orderTime) {
  if (!orderTime) return false
  
  const date = new Date(orderTime)
  const today = new Date()
  
  return date.toDateString() === today.toDateString()
}

/**
 * 判断是否为昨天的订单
 * @param {string|Date} orderTime - 订单时间
 * @returns {boolean}
 */
export function isYesterday(orderTime) {
  if (!orderTime) return false
  
  const date = new Date(orderTime)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return date.toDateString() === yesterday.toDateString()
}

/**
 * 获取订单时间的智能显示
 * @param {string|Date} orderTime - 订单时间
 * @returns {string}
 */
export function getSmartTimeDisplay(orderTime) {
  if (!orderTime) return '--'
  
  if (isToday(orderTime)) {
    return `今天 ${formatOrderTime(orderTime, 'time')}`
  }
  
  if (isYesterday(orderTime)) {
    return `昨天 ${formatOrderTime(orderTime, 'time')}`
  }
  
  const date = new Date(orderTime)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 7) {
    return formatOrderTime(orderTime, 'relative')
  }
  
  return formatOrderTime(orderTime, 'datetime')
}

/**
 * 格式化持续时间
 * @param {string|Date} startTime - 开始时间
 * @param {string|Date} endTime - 结束时间（可选，默认为当前时间）
 * @returns {string}
 */
export function formatDuration(startTime, endTime = new Date()) {
  if (!startTime) return '--'
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return '--'
  }
  
  const diffMs = end.getTime() - start.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`
  }
  
  const hours = Math.floor(diffMinutes / 60)
  const minutes = diffMinutes % 60
  
  if (minutes === 0) {
    return `${hours}小时`
  }
  
  return `${hours}小时${minutes}分钟`
}

/**
 * 获取订单状态的时间信息
 * @param {object} order - 订单对象
 * @returns {object}
 */
export function getOrderStatusTime(order) {
  const orderTime = order.orderTime || order.order_time
  const now = new Date()
  
  return {
    orderTime: getSmartTimeDisplay(orderTime),
    duration: formatDuration(orderTime, now),
    isRecent: isToday(orderTime),
    timestamp: orderTime
  }
}

/**
 * 按日期分组订单
 * @param {Array} orders - 订单列表
 * @returns {object} 按日期分组的订单
 */
export function groupOrdersByDate(orders) {
  const groups = {}
  
  orders.forEach(order => {
    const orderTime = order.orderTime || order.order_time
    if (!orderTime) return
    
    let groupKey
    if (isToday(orderTime)) {
      groupKey = '今天'
    } else if (isYesterday(orderTime)) {
      groupKey = '昨天'
    } else {
      groupKey = formatOrderTime(orderTime, 'date')
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    
    groups[groupKey].push(order)
  })
  
  // 按时间倒序排序每组内的订单
  Object.keys(groups).forEach(key => {
    groups[key].sort((a, b) => {
      const timeA = new Date(a.orderTime || a.order_time)
      const timeB = new Date(b.orderTime || b.order_time)
      return timeB.getTime() - timeA.getTime()
    })
  })
  
  return groups
}
