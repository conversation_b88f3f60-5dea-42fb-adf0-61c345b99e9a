# 📱 手机端菜单API - 基于真实数据库数据

## ⚠️ 重要说明

本文档基于**实际数据库数据**编写，反映了当前系统中的真实情况。

## 🔍 当前数据库状态

### 📋 分类数据 (dish_categories表)

当前系统中有以下分类：

| ID | 名称 | 描述 | 图片 | 餐品数量 |
|----|------|------|------|----------|
| 1 | 川菜 | 四川地方菜系，以麻辣鲜香著称 | 无 | 0 |
| 2 | 粤菜 | 广东地方菜系，注重原汁原味 | 无 | 0 |
| 3 | 湘菜 | 湖南地方菜系，口味偏重 | 无 | 0 |
| 4 | 鲁菜 | 山东地方菜系，讲究火候 | 无 | 0 |
| 5 | 苏菜 | 江苏地方菜系，清淡鲜美 | 无 | 0 |
| 6 | 浙菜 | 浙江地方菜系，清香爽脆 | 无 | 0 |
| 7 | 闽菜 | 福建地方菜系，汤鲜味美 | 无 | 0 |
| 8 | 徽菜 | 安徽地方菜系，重油重色 | 无 | 0 |
| 9 | 素食 | 素食菜品，健康养生 | 无 | 0 |

### 🍽️ 餐品数据 (dishes表)

当前系统中有以下餐品：

| ID | 名称 | 价格 | 分类 | 库存 | 销量 | 状态 |
|----|------|------|------|------|------|------|
| 1 | 宫保鸡丁 | ¥28.00 | 川菜 | 0 | 0 | 售罄 |
| 2 | 麻婆豆腐 | ¥18.00 | 川菜 | 0 | 0 | 售罄 |
| 3 | 回锅肉 | ¥32.00 | 川菜 | 0 | 0 | 售罄 |
| 4 | 白切鸡 | ¥35.00 | 粤菜 | 0 | 0 | 售罄 |
| 5 | 蒸蛋羹 | ¥12.00 | 粤菜 | 0 | 0 | 售罄 |
| 6 | 剁椒鱼头 | ¥48.00 | 湘菜 | 0 | 0 | 售罄 |
| 7 | 糖醋里脊 | ¥26.00 | 鲁菜 | 0 | 0 | 售罄 |
| 8 | 红烧狮子头 | ¥38.00 | 苏菜 | 0 | 0 | 售罄 |
| 9 | 西湖醋鱼 | ¥42.00 | 浙菜 | 0 | 0 | 售罄 |

## 📊 实际API响应示例

### 1. 获取分类列表

**请求**: `GET /api/mobile/menu/categories`

**实际响应**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "川菜",
            "parent_id": 0,
            "description": "四川地方菜系，以麻辣鲜香著称",
            "image": "",
            "sort_order": 1,
            "status": true,
            "dish_count": 0
        },
        {
            "id": 2,
            "name": "粤菜",
            "parent_id": 0,
            "description": "广东地方菜系，注重原汁原味",
            "image": "",
            "sort_order": 2,
            "status": true,
            "dish_count": 0
        }
    ]
}
```

### 2. 获取餐品列表

**请求**: `GET /api/mobile/menu/dishes?limit=3`

**实际响应**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "宫保鸡丁",
                "description": null,
                "price": 28.00,
                "main_image": "",
                "category_id": 1,
                "category_name": "川菜",
                "dish_type": "hot_dish",
                "dish_type_text": "热菜",
                "spicy_level": 0,
                "spicy_level_text": "不辣",
                "cooking_time": 0,
                "serving_size": "1人份",
                "sales": 0,
                "stock": 0,
                "stock_status": "out_of_stock",
                "stock_status_text": "暂时售罄",
                "can_order": false,
                "is_recommended": false,
                "is_spicy": false,
                "is_vegetarian": false,
                "nutrition": null,
                "ingredients": [],
                "allergens": [],
                "tags": [],
                "images": []
            },
            {
                "id": 2,
                "name": "麻婆豆腐",
                "description": null,
                "price": 18.00,
                "main_image": "",
                "category_id": 1,
                "category_name": "川菜",
                "dish_type": "hot_dish",
                "dish_type_text": "热菜",
                "spicy_level": 0,
                "spicy_level_text": "不辣",
                "cooking_time": 0,
                "serving_size": "1人份",
                "sales": 0,
                "stock": 0,
                "stock_status": "out_of_stock",
                "stock_status_text": "暂时售罄",
                "can_order": false,
                "is_recommended": false,
                "is_spicy": false,
                "is_vegetarian": false,
                "nutrition": null,
                "ingredients": [],
                "allergens": [],
                "tags": [],
                "images": []
            }
        ],
        "pagination": {
            "total": 9,
            "page": 1,
            "limit": 3,
            "pages": 3
        }
    }
}
```

### 3. 获取餐品详情

**请求**: `GET /api/mobile/menu/dishes/1`

**实际响应**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "name": "宫保鸡丁",
        "description": null,
        "price": 28.00,
        "main_image": "",
        "category_id": 1,
        "category_name": "川菜",
        "category_description": "四川地方菜系，以麻辣鲜香著称",
        "dish_type": "hot_dish",
        "dish_type_text": "热菜",
        "spicy_level": 0,
        "spicy_level_text": "不辣",
        "cooking_time": 0,
        "serving_size": "1人份",
        "sales": 0,
        "stock": 0,
        "stock_status": "out_of_stock",
        "stock_status_text": "暂时售罄",
        "can_order": false,
        "is_recommended": false,
        "is_spicy": false,
        "is_vegetarian": false,
        "nutrition": null,
        "ingredients": [],
        "allergens": [],
        "tags": [],
        "images": [],
        "related_dishes": []
    }
}
```

## 🚨 当前数据状态分析

### ❌ 发现的问题

1. **所有餐品库存为0** - 导致所有餐品显示"暂时售罄"，无法下单
2. **缺少图片** - 所有餐品和分类都没有图片
3. **缺少描述** - 大部分餐品没有描述信息
4. **缺少营养信息** - 所有餐品的营养信息为空
5. **缺少配料信息** - 所有餐品的配料列表为空
6. **缺少标签** - 所有餐品的标签为空
7. **销量为0** - 所有餐品销量为0
8. **辣度设置** - 川菜的辣度都设置为0（不辣）

### 📋 分类统计

- **总分类数**: 9个
- **有餐品的分类**: 6个 (川菜、粤菜、湘菜、鲁菜、苏菜、浙菜)
- **空分类**: 3个 (闽菜、徽菜、素食)

### 🍽️ 餐品统计

- **总餐品数**: 9个
- **可下单餐品**: 0个 (所有餐品库存为0)
- **推荐餐品**: 0个
- **素食餐品**: 0个
- **有图片餐品**: 0个

## 💡 改进建议

### 1. 数据完善建议

```sql
-- 更新库存，让餐品可以下单
UPDATE dishes SET stock = 50, min_stock = 10 WHERE id IN (1,2,3,4,5,6,7,8,9);

-- 设置推荐餐品
UPDATE dishes SET is_recommended = 1 WHERE id IN (1,2,4,6);

-- 设置川菜辣度
UPDATE dishes SET spicy_level = 2, is_spicy = 1 WHERE id IN (1,2,3);

-- 设置素食
UPDATE dishes SET is_vegetarian = 1 WHERE id IN (2,5);

-- 添加描述
UPDATE dishes SET description = '经典川菜，鸡肉嫩滑，花生香脆' WHERE id = 1;
UPDATE dishes SET description = '传统川菜，豆腐嫩滑，麻辣鲜香' WHERE id = 2;
UPDATE dishes SET description = '四川名菜，肥而不腻，香辣下饭' WHERE id = 3;

-- 设置销量
UPDATE dishes SET sales = FLOOR(RAND() * 100) + 10;
```

### 2. 图片资源建议

建议添加以下图片：
- 分类图片：每个菜系的代表性图片
- 餐品图片：每个餐品的实物图片
- 占位图片：当没有图片时的默认图片

### 3. 营养信息建议

为主要餐品添加营养信息：
```json
{
    "calories": "320kcal",
    "protein": "25g",
    "fat": "18g",
    "carbs": "12g",
    "fiber": "3g"
}
```

### 4. 配料信息建议

为餐品添加配料列表：
```json
["鸡胸肉", "花生米", "青椒", "红椒", "葱", "姜", "蒜"]
```

## 🧪 测试建议

### 1. 添加测试数据

为了更好地演示API功能，建议：
1. 设置部分餐品有库存
2. 设置部分餐品为推荐
3. 添加餐品图片
4. 完善餐品描述

### 2. 测试场景

- 正常浏览：有库存的餐品
- 售罄处理：库存为0的餐品
- 推荐展示：推荐餐品列表
- 搜索功能：关键词搜索
- 分类筛选：按分类浏览

## 📞 技术支持

如需完善数据或有其他问题，请联系开发团队。

---

**文档版本**: v1.0 (基于真实数据)  
**更新时间**: 2024-12-19  
**数据检查时间**: 实时
