<template>
  <div class="city-selector">
    <!-- 导航栏 -->
    <header class="header">
      <button class="back-btn" @click="goBack">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
      </button>
      <h1 class="title">选择城市</h1>
      <div class="placeholder"></div>
    </header>

    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-box">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="M21 21l-4.35-4.35"/>
        </svg>
        <input 
          v-model="searchKeyword" 
          class="search-input" 
          placeholder="搜索城市"
        >
        <button v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="locationStore.isCitiesLoading" class="loading-state">
      <div class="loading-spinner">🔄</div>
      <p>正在加载城市列表...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="locationStore.hasCitiesError" class="error-state">
      <p>{{ locationStore.citiesError }}</p>
      <button @click="reloadCities" class="retry-btn">重新加载</button>
    </div>

    <!-- 搜索结果 -->
    <div v-else-if="searchKeyword" class="search-results">
      <div v-if="searchResults.length > 0" class="results-list">
        <div
          v-for="city in searchResults"
          :key="city.name"
          class="result-item"
          @click="selectCity(city)"
        >
          <span class="city-name">{{ city.name }}</span>
          <span v-if="city.name === currentCity" class="selected-mark">✓</span>
        </div>
      </div>
      <div v-else class="no-results">
        <div class="no-results-text">无结果</div>
      </div>
    </div>

    <!-- 城市列表 -->
    <div v-else-if="locationStore.hasCities" class="cities-content">
      <div
        v-for="(cities, letter) in citiesByLetter"
        :key="letter"
        class="letter-section"
        :data-letter="letter"
      >
        <div class="letter-title">{{ letter }}</div>
        <div class="cities-group">
          <div
            v-for="city in cities"
            :key="city.name"
            class="city-item"
            @click="selectCity(city)"
          >
            <span class="city-name">{{ city.name }}</span>
            <span v-if="city.name === currentCity" class="selected-mark">📍</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 无城市数据状态 -->
    <div v-else class="no-cities-state">
      <div class="no-cities-icon">🏙️</div>
      <p>暂无城市数据</p>
      <button @click="reloadCities" class="retry-btn">重新加载</button>
    </div>

    <!-- 右侧字母索引 -->
    <div v-if="!searchKeyword" class="alphabet-nav">
      <div
        v-for="letter in availableLetters"
        :key="letter"
        class="nav-letter"
        :class="{ active: letter === activeLetter }"
        @click="jumpToLetter(letter)"
        :title="`跳转到${letter}`"
      >
        {{ letter }}
      </div>
    </div>


  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useLocationStore } from '../stores/location'

export default {
  name: 'CitySelector',
  setup() {
    const router = useRouter()
    const locationStore = useLocationStore()
    
    // 响应式数据
    const searchKeyword = ref('')
    const activeLetter = ref('A')
    
    // 计算属性
    const currentCity = computed(() => locationStore.currentCity)
    const allCities = computed(() => locationStore.allCities)
    
    const searchResults = computed(() => {
      if (!searchKeyword.value.trim()) return []
      const keyword = searchKeyword.value.toLowerCase()
      return allCities.value.filter(city => 
        city.name.toLowerCase().includes(keyword)
      )
    })
    
    const citiesByLetter = computed(() => {
      const grouped = {}
      allCities.value.forEach(city => {
        const letter = city.first_char
        if (!grouped[letter]) {
          grouped[letter] = []
        }
        grouped[letter].push(city)
      })
      return grouped
    })
    
    const availableLetters = computed(() => {
      return Object.keys(citiesByLetter.value).sort()
    })
    
    // 方法
    const goBack = () => {
      router.push('/')
    }
    
    const selectCity = async (cityInfo) => {
      try {
        // cityInfo 可能是字符串（向后兼容）或对象
        const city = typeof cityInfo === 'string' ? { name: cityInfo } : cityInfo

        await locationStore.setCurrentCity(city)
        router.push('/')
      } catch (error) {
        console.error('切换城市失败:', error)
        alert('切换城市失败: ' + error.message)
      }
    }
    
    const clearSearch = () => {
      searchKeyword.value = ''
    }

    const reloadCities = async () => {
      try {
        await locationStore.fetchCitiesList()
      } catch (error) {
        console.error('重新加载城市失败:', error)
      }
    }
    
    const jumpToLetter = (letter) => {
      const element = document.querySelector(`[data-letter="${letter}"]`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
    
    // 滚动处理 - 简化可靠版本
    const handleScroll = () => {
      if (searchKeyword.value) return

      const sections = document.querySelectorAll('.letter-section')
      if (sections.length === 0) return

      let current = null

      // 从上到下遍历所有section，找到最后一个顶部在视口上方的section
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i]
        const rect = section.getBoundingClientRect()

        // 如果section的顶部还在视口下方，说明前面的section是当前的
        if (rect.top > 100) { // 100px的偏移量
          break
        }

        current = section.dataset.letter
      }

      // 如果没有找到合适的，使用第一个
      if (!current && availableLetters.value.length > 0) {
        current = availableLetters.value[0]
      }

      if (current && current !== activeLetter.value) {
        activeLetter.value = current
      }
    }

    // 防抖处理
    let scrollTimer = null
    const debouncedHandleScroll = () => {
      if (scrollTimer) {
        clearTimeout(scrollTimer)
      }
      scrollTimer = setTimeout(handleScroll, 50)
    }
    
    // 生命周期
    onMounted(async () => {
      // 首先加载城市数据
      if (!locationStore.hasCities && !locationStore.isCitiesLoading) {
        try {
          await locationStore.fetchCitiesList()
        } catch (error) {
          console.error('初始化城市数据失败:', error)
        }
      }

      // 等待城市数据加载完成后再初始化字母索引
      setTimeout(() => {
        if (availableLetters.value.length > 0) {
          activeLetter.value = availableLetters.value[0]
        }

        // 使用防抖版本的滚动监听
        window.addEventListener('scroll', debouncedHandleScroll, { passive: true })

        // 延迟初始化，确保DOM已渲染
        setTimeout(() => {
          handleScroll()
        }, 200)
      }, 100)
    })

    onBeforeUnmount(() => {
      window.removeEventListener('scroll', debouncedHandleScroll)
      if (scrollTimer) {
        clearTimeout(scrollTimer)
      }
    })
    
    return {
      locationStore,
      searchKeyword,
      activeLetter,
      currentCity,
      searchResults,
      citiesByLetter,
      availableLetters,
      goBack,
      selectCity,
      clearSearch,
      jumpToLetter,
      reloadCities
    }
  }
}
</script>

<style scoped>
.city-selector {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
}

/* 导航栏 */
.header {
  background: white;
  padding: 0 16px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  color: #007AFF;
  padding: 8px;
  border-radius: 6px;
  transition: opacity 0.2s;
}

.back-btn:hover {
  opacity: 0.6;
}

.title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.placeholder {
  width: 44px;
}

/* 搜索栏 */
.search-section {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e5;
}

.search-box {
  background: #f0f0f0;
  border-radius: 8px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-icon {
  color: #999;
}

.search-input {
  flex: 1;
  background: transparent;
  font-size: 16px;
}

.clear-btn {
  background: none;
  color: #999;
  padding: 2px;
}

/* 搜索结果 */
.search-results {
  background: white;
  margin: 12px 16px;
  border-radius: 8px;
  overflow: hidden;
}

.result-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background 0.2s;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:hover {
  background: #f8f8f8;
}

.city-name {
  font-size: 16px;
  color: #000;
  flex: 1;
}

.selected-mark {
  color: #007AFF;
  font-weight: 600;
}

.no-results {
  padding: 40px;
  text-align: center;
  color: #999;
}

/* 城市列表 */
.cities-content {
  background: white;
  margin: 12px 16px 12px;
  border-radius: 8px;
  overflow: hidden;
  padding-right: 30px;
}

.letter-section {
  border-bottom: 1px solid #f0f0f0;
}

.letter-section:last-child {
  border-bottom: none;
}

.letter-title {
  background: #f8f8f8;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  position: sticky;
  top: 44px;
  z-index: 10;
}

.city-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background 0.2s;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item:hover {
  background: #f8f8f8;
}

/* 右侧字母索引 */
.alphabet-nav {
  position: fixed;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 8px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 200;
}

.nav-letter {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #007AFF;
  cursor: pointer;
  transition: all 0.2s;
  margin: 1px 0;
}

.nav-letter:hover {
  background: rgba(0, 122, 255, 0.1);
  border-radius: 6px;
}

.nav-letter.active {
  background: #007AFF;
  color: white;
  border-radius: 6px;
  transform: scale(1.1);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  font-size: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: #ff6b35;
}

.retry-btn {
  background: #007AFF;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 16px;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 无城市数据状态 */
.no-cities-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-cities-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}


</style>
