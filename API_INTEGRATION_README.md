# 餐品API对接说明 - 基于真实数据库

## 概述

本项目已完全基于真实数据库API文档重新完善，实现了与真实菜单API的完整对接。系统能够正确处理真实的数据状态，包括售罄商品、库存管理等实际业务场景。

## ⚠️ 重要说明

**当前数据库状态**: 所有餐品库存为0，处于售罄状态，这是基于真实数据库的实际情况。

## 📊 真实数据库状态

### 当前数据概况
- **总分类数**: 9个 (川菜、粤菜、湘菜、鲁菜、苏菜、浙菜、闽菜、徽菜、素食)
- **总餐品数**: 9个
- **可订购餐品**: 0个 (全部售罄)
- **推荐餐品**: 0个
- **素食餐品**: 0个

### 发现的问题
1. ❌ **所有餐品库存为0** - 导致无法下单
2. ❌ **缺少图片资源** - 所有餐品和分类都没有图片
3. ❌ **缺少描述信息** - 大部分餐品没有描述
4. ❌ **缺少营养信息** - 所有餐品的营养信息为空
5. ❌ **辣度设置问题** - 川菜的辣度都设置为0（不辣）

### 用户体验优化
1. ✅ **完全匿名下单** - 无需任何用户信息即可下单
2. ✅ **简化API参数** - 移除 customer_name 和 customer_phone 参数
3. ✅ **一键下单** - 极简下单流程，提升用户体验
4. ✅ **服务端识别** - 用户身份由服务端自动处理
5. ✅ **智能时间显示** - 支持 order_time 字段的多种时间格式显示

## 已对接的API接口

### 1. 菜单分类接口 ✅
- **接口地址**: `GET /api/mobile/menu/categories`
- **对接方法**: `apiService.getMenuCategories(params)`
- **用途**: 获取菜品分类列表
- **真实响应**: 返回9个分类，包含完整的分类信息

### 2. 餐品列表接口 ✅
- **接口地址**: `GET /api/mobile/menu/dishes`
- **对接方法**: `apiService.getDishes(params)`
- **用途**: 获取餐品列表，支持分页和筛选
- **真实响应**: 返回9个餐品，全部售罄状态
- **已实现功能**:
  - 分页查询
  - 库存状态显示
  - 售罄状态处理
  - 真实价格显示

### 3. 餐品详情接口 ✅
- **接口地址**: `GET /api/mobile/menu/dishes/{id}`
- **对接方法**: `apiService.getDishDetail(dishId)`
- **用途**: 获取单个餐品的详细信息
- **真实响应**: 包含完整的餐品详情和相关信息

### 4. 推荐餐品接口 ✅
- **接口地址**: `GET /api/mobile/menu/recommended`
- **对接方法**: `apiService.getRecommendedDishes(limit)`
- **用途**: 获取推荐餐品列表
- **当前状态**: 无推荐餐品

### 5. 搜索餐品接口 ✅
- **接口地址**: `GET /api/mobile/menu/search`
- **对接方法**: `apiService.searchDishes(keyword, page, limit)`
- **用途**: 根据关键词搜索餐品
- **已实现功能**:
  - 关键词搜索
  - 分页支持
  - 防抖处理

## 📋 订单API接口

### 1. 创建订单接口 ✅
- **接口地址**: `POST /api/mobile/orders`
- **对接方法**: `apiService.createOrder(orderData)`
- **用途**: 创建新订单
- **用户体验**: 使用默认用户信息，简化用户输入
- **请求数据**:
  ```json
  {
    "customer_name": "顾客",
    "customer_phone": "13800000000",
    "items": [
      {
        "dish_id": 1,
        "quantity": 2,
        "special_requirements": "不要辣"
      }
    ],
    "dining_type": "dine_in",
    "table_no": "A01",
    "remark": "请尽快制作"
  }
  ```
- **说明**: 根据API文档调整字段名，使用默认用户信息
- **时间字段**: 支持 `order_time` 字段，提供智能时间显示

### 2. 获取订单详情接口 ✅
- **接口地址**: `GET /api/mobile/orders/{id}`
- **对接方法**: `apiService.getOrderDetail(orderId)`
- **用途**: 获取单个订单的详细信息

### 3. 获取用户订单列表接口 ✅
- **接口地址**: `GET /api/mobile/orders`
- **对接方法**: `apiService.getUserOrders(customerPhone, status, page, limit)`
- **用途**: 获取用户的订单列表
- **支持参数**:
  - customer_phone: 用户手机号
  - status: 订单状态筛选
  - page: 页码
  - limit: 每页数量

### 4. 取消订单接口 ✅
- **接口地址**: `PUT /api/mobile/orders/{id}/cancel`
- **对接方法**: `apiService.cancelOrder(orderId, cancelReason)`
- **用途**: 取消指定订单

### 5. 获取订单状态接口 ✅
- **接口地址**: `GET /api/mobile/orders/{id}/status`
- **对接方法**: `apiService.getOrderStatus(orderId)`
- **用途**: 获取订单当前状态
- **支持轮询**: `apiService.pollOrderStatus(orderId, callback, interval)`
- **降级处理**: 如果状态接口失败，自动使用订单详情接口获取状态
- **错误处理**: 连续失败3次后停止轮询，可选择启动状态模拟

## 🕒 时间字段处理

### order_time 字段支持
- **字段名**: `order_time` (后端新增字段)
- **格式**: ISO 8601 时间字符串
- **用途**: 记录订单创建的准确时间

### 时间显示功能
```javascript
// 智能时间显示
getSmartTimeDisplay(orderTime)
// 输出示例:
// "刚刚" | "5分钟前" | "今天 14:30" | "昨天 09:15" | "2024-12-19 14:30"

// 订单时间信息
getOrderStatusTime(order)
// 返回: { display, isRecent, timestamp }
```

### 时间工具函数 (`src/utils/dateUtils.js`)
- `formatOrderTime()` - 多格式时间显示
- `getSmartTimeDisplay()` - 智能时间显示
- `groupOrdersByDate()` - 按日期分组订单
- `isToday()` / `isYesterday()` - 日期判断

## 文件结构

```
src/
├── config/
│   └── api.js                 # API配置文件（菜单+订单API）
├── services/
│   └── api.js                 # API服务类（菜单+订单API完整对接）
├── stores/
│   └── orders.js              # 订单状态管理（集成真实API）
├── utils/
│   ├── phoneUtils.js          # 手机号处理工具
│   └── dateUtils.js           # 时间处理工具（新增）
├── views/
│   ├── MenuPage.vue           # 菜单页面（处理真实库存状态）
│   ├── Home.vue               # 首页（对接搜索API）
│   ├── OrderConfirm.vue       # 订单确认页面（使用真实API创建订单）
│   ├── OrderStatus.vue        # 订单状态页面（实时状态轮询+时间显示）
│   ├── OrdersList.vue         # 订单列表页面（获取用户订单+智能时间）
│   ├── OrderSuccess.vue       # 点餐成功页面
│   ├── ApiTest.vue            # 菜单API测试页面
│   ├── OrderApiTest.vue       # 订单API测试页面（时间格式化）
│   └── DatabaseStatus.vue    # 数据库状态监控页面
└── components/
    └── ProxyStatus.vue        # 代理状态组件
```

## 核心功能实现

### 1. 菜单页面 (MenuPage.vue)
- ✅ 从API获取餐品数据
- ✅ 显示餐品信息（名称、描述、价格、标签）
- ✅ 支持素食、辣度、推荐标签显示
- ✅ 库存管理和数量限制
- ✅ 降级处理（API失败时使用模拟数据）

### 2. 搜索功能 (Home.vue)
- ✅ 实时搜索餐品
- ✅ 防抖处理避免频繁请求
- ✅ 搜索结果展示

### 3. API服务 (api.js)
- ✅ 统一的请求处理
- ✅ 错误处理和重试机制
- ✅ 响应格式验证
- ✅ 环境配置支持

## API配置

### 开发环境
```javascript
MENU_API_BASE_URL: 'http://localhost:8000'
```

### 生产环境
```javascript
MENU_API_BASE_URL: 'https://api.example.com'
```

## 使用方法

### 1. 启动API服务器
确保菜单API服务器在 `http://localhost:8000` 运行

### 2. 测试API连接
访问 `/api-test` 页面测试各个API接口的连接状态

### 3. 查看菜单数据
访问 `/menu/1` 页面查看从API获取的真实菜品数据

## 数据映射

### API响应 → 前端数据结构
```javascript
// API响应
{
  "id": 1,
  "name": "宫保鸡丁",
  "description": "经典川菜",
  "price": 28.00,
  "main_image": "image_url",
  "category_id": 1,
  "category_name": "热菜",
  "stock": 10,
  "spicy_level": 2,
  "spicy_level_text": "中辣",
  "is_spicy": true,
  "is_vegetarian": false,
  "is_recommended": true,
  "sales": 156,
  "stock_status": 1,
  "can_order": true
}

// 前端数据结构
{
  id: 1,
  name: "宫保鸡丁",
  description: "经典川菜",
  price: 28.00,
  image: "image_url",
  category_id: 1,
  category_name: "热菜",
  maxQuantity: 10,
  phone: "13812345678", // 生成的手机号
  spicy_level: 2,
  spicy_level_text: "中辣",
  is_spicy: true,
  is_vegetarian: false,
  is_recommended: true,
  sales: 156,
  stock_status: 1,
  can_order: true
}
```

## 错误处理

### 1. 网络错误
- 自动重试机制
- 降级到模拟数据
- 用户友好的错误提示

### 2. API错误
- 检查响应状态码
- 解析错误消息
- 记录错误日志

### 3. 数据错误
- 数据格式验证
- 默认值处理
- 空数据处理

## 便捷方法

除了基础API方法外，还提供了以下便捷方法：

```javascript
// 根据分类获取餐品
apiService.getDishesByCategory(categoryId, page, limit)

// 获取素食餐品
apiService.getVegetarianDishes(page, limit)

// 获取分类推荐餐品
apiService.getRecommendedDishesByCategory(categoryId, limit)
```

## 测试说明

### API测试页面功能
- 测试所有菜单API接口
- 显示API响应数据
- 检查连接状态
- 错误信息展示

### 访问方式
```
http://localhost:3001/api-test
```

## 注意事项

1. **API服务器**: 确保菜单API服务器正常运行
2. **跨域问题**: 开发环境已配置代理，生产环境需要配置CORS
3. **数据格式**: 严格按照API文档的响应格式处理数据
4. **错误处理**: 所有API调用都有完善的错误处理机制
5. **性能优化**: 搜索功能使用防抖处理，避免频繁请求

## 后续扩展

1. **缓存机制**: 可以添加本地缓存减少API请求
2. **离线支持**: 可以实现离线数据存储
3. **实时更新**: 可以添加WebSocket支持实时数据更新
4. **图片优化**: 可以添加图片懒加载和压缩
5. **分类管理**: 可以添加动态分类筛选功能
