import { API_CONFIG, getCurrentConfig } from '@/config/api.js'

// API服务模块 - 基于官方API文档v4.0 + 手机端菜单API (代理模式)
class ApiService {
  constructor() {
    this.baseURL = '/api'
    const config = getCurrentConfig()
    // 在开发环境使用代理，生产环境使用完整URL
    this.menuBaseURL = config.MENU_API_BASE_URL || '' // 代理模式下为空字符串
  }

  // 通用请求方法
  async request(url, options = {}) {
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      timeout: 10000, // 10秒超时
      ...options
    }

    try {
      const response = await fetch(`${this.baseURL}${url}`, config)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 根据API文档的标准响应格式处理
      if (data.code !== 200) {
        throw new Error(data.message || '请求失败')
      }

      return data.data
    } catch (error) {
      console.error('API请求失败:', error)
      this.handleApiError(error)
      throw error
    }
  }

  // 错误处理方法
  handleApiError(error) {
    if (error.message.includes('400')) {
      console.error('请求参数错误')
    } else if (error.message.includes('404')) {
      console.error('资源不存在')
    } else if (error.message.includes('500')) {
      console.error('服务器内部错误')
    }
  }

  // GET请求
  async get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const fullUrl = queryString ? `${url}?${queryString}` : url

    return this.request(fullUrl, {
      method: 'GET'
    })
  }

  // POST请求
  async post(url, data = {}) {
    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // 菜单API专用请求方法 - 代理模式
  async menuRequest(endpoint, options = {}) {
    // 使用代理模式，直接使用相对路径
    const url = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`

    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
    }

    const config = { ...defaultOptions, ...options }

    try {
      console.log(`🔗 菜单API请求 (代理): ${url}`)
      const response = await fetch(url, config)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`📊 API响应:`, result)

      // 检查真实API响应格式: { code: 200, message: "success", data: ... }
      if (result.code !== 200) {
        throw new Error(result.message || 'API请求失败')
      }

      return result.data
    } catch (error) {
      console.error('❌ 菜单API请求失败:', error)
      // 如果是网络错误，提供更友好的错误信息
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('无法连接到菜单服务器，请检查代理配置')
      }
      throw error
    }
  }

  // 1. 附近门店搜索 (推荐) - 根据API文档
  async getNearbyStores(longitude, latitude, options = {}) {
    const params = {
      longitude: longitude.toString(),
      latitude: latitude.toString(),
      city_only: options.cityOnly !== undefined ? (options.cityOnly ? 1 : 0) : 1,
      limit: options.limit || 0,
      radius: options.radius || 50
    }

    // 添加可选参数
    if (options.status) {
      params.status = options.status
    }

    return this.get('/stores/nearby', params)
  }

  // 2. 门店列表 - 根据API文档
  async getStoresList(options = {}) {
    const params = {
      page: options.page || 1,
      limit: options.limit || 20
    }

    if (options.status) {
      params.status = options.status
    }

    return this.get('/stores', params)
  }

  // 3. 按城市搜索门店 - 根据API文档
  async searchStores(options = {}) {
    const params = {}

    if (options.city) {
      params.city = options.city
    }
    // 支持两种格式：cityCode 和 city_code
    if (options.cityCode || options.city_code) {
      params.city_code = options.cityCode || options.city_code
    }
    if (options.keyword) {
      params.keyword = options.keyword
    }

    return this.get('/stores/search', params)
  }

  // 4. 根据城市代码获取门店 - 根据API文档
  async getStoresByCityCode(cityCode) {
    return this.get(`/stores/by-city/${cityCode}`)
  }

  // 5. 门店详情 - 根据API文档
  async getStoreDetail(storeId) {
    return this.get(`/stores/${storeId}`)
  }

  // 6. 获取城市列表 - 使用专门的城市API
  async getCitiesList(options = {}) {
    try {
      const params = {}
      if (options.firstChar) {
        params.first_char = options.firstChar
      }
      if (options.keyword) {
        params.keyword = options.keyword
      }

      const citiesData = await this.get('/cities', params)

      if (!Array.isArray(citiesData)) {
        throw new Error('城市数据格式错误')
      }

      // 转换为统一格式
      const cities = citiesData.map(city => ({
        name: city.city_name,
        code: city.city_code,
        first_char: city.first_char,
        pinyin: city.city_pinyin,
        province: city.province,
        level: city.level
      }))

      return cities

    } catch (error) {
      console.error('获取城市列表失败:', error)
      throw error
    }
  }

  // 7. 获取分组城市列表 - 专门用于城市选择器
  async getGroupedCities() {
    try {
      const groupedData = await this.get('/cities/grouped')

      if (!groupedData) {
        throw new Error('分组城市数据为空')
      }

      let citiesList = []

      if (Array.isArray(groupedData)) {
        // 如果API返回的是数组（实际情况），我们需要手动分组
        citiesList = groupedData
      } else if (typeof groupedData === 'object') {
        // 如果API返回的是分组对象（文档描述的格式）
        Object.keys(groupedData).forEach(letter => {
          if (Array.isArray(groupedData[letter])) {
            citiesList.push(...groupedData[letter])
          }
        })
      } else {
        throw new Error('分组城市数据格式不支持')
      }

      // 手动按首字母分组
      const groupedCities = {}

      citiesList.forEach(city => {
        const convertedCity = {
          name: city.city_name,
          code: city.city_code,
          first_char: city.first_char,
          pinyin: city.city_pinyin
        }

        const letter = city.first_char || 'A'
        if (!groupedCities[letter]) {
          groupedCities[letter] = []
        }
        groupedCities[letter].push(convertedCity)
      })

      return groupedCities

    } catch (error) {
      console.error('获取分组城市列表失败:', error)
      throw error
    }
  }

  // 8. 获取城市详情
  async getCityDetail(cityCode) {
    try {
      const cityData = await this.get(`/cities/${cityCode}`)

      if (!cityData) {
        throw new Error('城市详情数据为空')
      }

      // 如果API返回的是数组，查找匹配的城市
      if (Array.isArray(cityData)) {
        const targetCity = cityData.find(city => city.city_code === cityCode)
        if (!targetCity) {
          throw new Error(`未找到城市代码为 ${cityCode} 的城市`)
        }

        // 转换为统一格式
        return {
          name: targetCity.city_name,
          code: targetCity.city_code,
          first_char: targetCity.first_char,
          pinyin: targetCity.city_pinyin,
          province: targetCity.province,
          level: targetCity.level,
          longitude: targetCity.longitude,
          latitude: targetCity.latitude
        }
      } else {
        // 如果API返回的是单个对象
        return {
          name: cityData.city_name,
          code: cityData.city_code,
          first_char: cityData.first_char,
          pinyin: cityData.city_pinyin,
          province: cityData.province,
          level: cityData.level,
          longitude: cityData.longitude,
          latitude: cityData.latitude
        }
      }

    } catch (error) {
      console.error('获取城市详情失败:', error)
      throw error
    }
  }

  // 获取中文首字母
  getFirstChar(str) {
    if (!str) return 'A'

    const char = str.charAt(0)

    // 中文字符首字母映射
    const charMap = {
      '阿': 'A', '安': 'A', '鞍': 'A',
      '北': 'B', '包': 'B', '保': 'B', '蚌': 'B', '白': 'B',
      '成': 'C', '重': 'C', '长': 'C', '常': 'C', '沧': 'C', '承': 'C',
      '大': 'D', '东': 'D', '丹': 'D', '德': 'D',
      '鄂': 'E',
      '福': 'F', '佛': 'F', '抚': 'F',
      '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G',
      '杭': 'H', '哈': 'H', '合': 'H', '海': 'H', '呼': 'H', '黄': 'H', '惠': 'H', '邯': 'H', '衡': 'H',
      '济': 'J', '金': 'J', '嘉': 'J', '江': 'J', '九': 'J', '吉': 'J', '锦': 'J',
      '昆': 'K', '开': 'K',
      '兰': 'L', '洛': 'L', '临': 'L', '柳': 'L', '连': 'L', '辽': 'L', '廊': 'L', '六': 'L',
      '马': 'M', '牡': 'M', '绵': 'M',
      '南': 'N', '宁': 'N', '内': 'N',
      '盘': 'P', '平': 'P', '莆': 'P',
      '青': 'Q', '泉': 'Q', '秦': 'Q', '齐': 'Q',
      '上': 'S', '深': 'S', '苏': 'S', '沈': 'S', '石': 'S', '三': 'S', '汕': 'S', '韶': 'S', '绍': 'S', '十': 'S',
      '天': 'T', '太': 'T', '唐': 'T', '台': 'T', '泰': 'T', '铁': 'T',
      '乌': 'U',
      '武': 'W', '无': 'W', '温': 'W', '威': 'W', '潍': 'W', '芜': 'W', '梧': 'W',
      '西': 'X', '厦': 'X', '徐': 'X', '新': 'X', '襄': 'X', '湘': 'X', '咸': 'X', '信': 'X',
      '银': 'Y', '烟': 'Y', '扬': 'Y', '宜': 'Y', '岳': 'Y', '营': 'Y', '榆': 'Y',
      '郑': 'Z', '珠': 'Z', '中': 'Z', '淄': 'Z', '株': 'Z', '张': 'Z', '湛': 'Z', '肇': 'Z', '舟': 'Z', '漳': 'Z'
    }

    return charMap[char] || char.toUpperCase()
  }

  // 便捷方法：根据城市代码获取门店（推荐使用）
  async getStoresByCityInfo(cityInfo, options = {}) {
    try {
      // 优先使用搜索接口的 city_code 参数（按照API文档推荐）
      if (cityInfo.code) {
        try {
          const searchResult = await this.searchStores({
            city_code: cityInfo.code,
            ...options
          })

          if (searchResult && searchResult.stores && searchResult.stores.length > 0) {
            // 确保返回的城市信息正确
            if (!searchResult.current_city) {
              searchResult.current_city = {
                city_name: cityInfo.name,
                city_code: cityInfo.code
              }
            }
            return searchResult
          }
        } catch (error) {
          // 搜索接口可能还未完全实现，继续尝试其他方法
        }
      }

      // 降级到城市代码专用接口
      if (cityInfo.code) {
        try {
          const result = await this.getStoresByCityCode(cityInfo.code)

          if (result && Array.isArray(result) && result.length > 0) {
            // API直接返回门店数组，需要包装成标准格式
            return {
              stores: result,
              current_city: {
                city_name: cityInfo.name,
                city_code: cityInfo.code
              }
            }
          } else if (result && result.stores && result.stores.length > 0) {
            // 如果API返回的是包装格式
            if (!result.current_city) {
              result.current_city = {
                city_name: cityInfo.name,
                city_code: cityInfo.code
              }
            }
            return result
          }
        } catch (error) {
          // 继续尝试其他方法
        }
      }

      // 最后降级到搜索接口使用城市名称
      if (cityInfo.name) {
        try {
          const searchResult = await this.searchStores({
            city: cityInfo.name,
            ...options
          })

          if (searchResult && searchResult.stores && searchResult.stores.length > 0) {
            // 确保返回的城市信息正确
            if (!searchResult.current_city) {
              searchResult.current_city = {
                city_name: cityInfo.name,
                city_code: cityInfo.code || ''
              }
            }
            return searchResult
          }
        } catch (error) {
          // 继续尝试其他方法
        }
      }

      // 最终降级到附近门店接口
      if (cityInfo.name) {
        try {
          const coordinates = await this.getCityCoordinates(cityInfo.name, cityInfo.code)
          const nearbyResult = await this.getNearbyStores(coordinates.longitude, coordinates.latitude, {
            cityOnly: true,
            limit: options.limit || 0,
            ...options
          })

          // 确保返回的城市信息正确
          if (nearbyResult.current_city) {
            nearbyResult.current_city.city_name = cityInfo.name
            if (cityInfo.code) {
              nearbyResult.current_city.city_code = cityInfo.code
            }
          } else {
            nearbyResult.current_city = {
              city_name: cityInfo.name,
              city_code: cityInfo.code || ''
            }
          }

          return nearbyResult
        } catch (error) {
          // 最后的尝试也失败了
        }
      }

      throw new Error('所有获取门店的方式都失败了')
    } catch (error) {
      console.error('根据城市信息获取门店失败:', error)
      throw error
    }
  }

  // 兼容方法：根据城市名获取门店（保持向后兼容）
  async getStoresByCity(cityName, options = {}) {
    return this.getStoresByCityInfo({ name: cityName }, options)
  }

  // 根据城市名获取坐标（优先使用城市API，降级到硬编码映射）
  async getCityCoordinates(cityName, cityCode = null) {
    try {
      // 优先尝试从城市API获取坐标
      if (cityCode) {
        try {
          const cityDetail = await this.getCityDetail(cityCode)
          if (cityDetail && cityDetail.longitude && cityDetail.latitude) {
            return {
              longitude: parseFloat(cityDetail.longitude),
              latitude: parseFloat(cityDetail.latitude)
            }
          }
        } catch (error) {
          // 继续尝试其他方法
        }
      }

      // 降级到城市列表API搜索
      try {
        const cities = await this.getCitiesList({ keyword: cityName })
        const matchedCity = cities.find(city =>
          city.name === cityName ||
          city.name.includes(cityName) ||
          cityName.includes(city.name.replace('市', ''))
        )

        if (matchedCity && matchedCity.longitude && matchedCity.latitude) {
          return {
            longitude: parseFloat(matchedCity.longitude),
            latitude: parseFloat(matchedCity.latitude)
          }
        }
      } catch (error) {
        // 继续尝试其他方法
      }

      // 最后降级到硬编码映射表
      const cityCoordinates = {
        '北京': { longitude: 116.407526, latitude: 39.904030 },
        '北京市': { longitude: 116.407526, latitude: 39.904030 },
        '上海': { longitude: 121.473701, latitude: 31.230416 },
        '上海市': { longitude: 121.473701, latitude: 31.230416 },
        '广州': { longitude: 113.264434, latitude: 23.129162 },
        '深圳': { longitude: 114.064665, latitude: 22.548447 },
        '郑州': { longitude: 113.625368, latitude: 34.746599 },
        '杭州': { longitude: 120.153576, latitude: 30.287459 },
        '南京': { longitude: 118.767413, latitude: 32.041544 },
        '武汉': { longitude: 114.298572, latitude: 30.584355 },
        '成都': { longitude: 104.066541, latitude: 30.572269 },
        '重庆': { longitude: 106.551556, latitude: 29.562849 },
        '天津': { longitude: 117.200983, latitude: 39.084158 },
        '西安': { longitude: 108.940175, latitude: 34.341568 },
        '沈阳': { longitude: 123.432791, latitude: 41.808645 },
        '青岛': { longitude: 120.382639, latitude: 36.067082 },
        '大连': { longitude: 121.593478, latitude: 38.94871 },
        '厦门': { longitude: 118.094909, latitude: 24.479834 },
        '福州': { longitude: 119.296494, latitude: 26.074507 },
        '昆明': { longitude: 102.714601, latitude: 25.049153 },
        '长沙': { longitude: 112.979353, latitude: 28.213478 },
        '合肥': { longitude: 117.227239, latitude: 31.820587 },
        '济南': { longitude: 117.024967, latitude: 36.682785 },
        // 添加更多城市坐标
        '阿拉善盟': { longitude: 105.728969, latitude: 38.851892 },
        '呼和浩特': { longitude: 111.749181, latitude: 40.842585 },
        '包头': { longitude: 109.840347, latitude: 40.658168 },
        '乌鲁木齐': { longitude: 87.616848, latitude: 43.825592 },
        '拉萨': { longitude: 91.117212, latitude: 29.647535 },
        '银川': { longitude: 106.206479, latitude: 38.502621 },
        '西宁': { longitude: 101.76792, latitude: 36.640739 },
        '兰州': { longitude: 103.823305, latitude: 36.064225 },
        '石家庄': { longitude: 114.522082, latitude: 38.048958 },
        '太原': { longitude: 112.550864, latitude: 37.890277 },
        '哈尔滨': { longitude: 126.657717, latitude: 45.773225 },
        '长春': { longitude: 125.313642, latitude: 43.898338 },
        '南昌': { longitude: 115.893528, latitude: 28.689578 },
        '贵阳': { longitude: 106.709177, latitude: 26.629907 },
        '海口': { longitude: 110.330802, latitude: 20.022071 }
      }

      // 尝试精确匹配和模糊匹配
      let coordinates = cityCoordinates[cityName]
      if (!coordinates) {
        // 尝试去掉"市"后缀匹配
        const cityNameWithoutSuffix = cityName.replace(/[市县区]$/, '')
        coordinates = cityCoordinates[cityNameWithoutSuffix]
      }
      if (!coordinates) {
        // 尝试添加"市"后缀匹配
        coordinates = cityCoordinates[cityName + '市']
      }

      if (coordinates) {
        return coordinates
      } else {
        // 如果都找不到，返回默认坐标（北京）
        return { longitude: 116.407526, latitude: 39.904030 }
      }

    } catch (error) {
      // 返回默认坐标（北京）
      return { longitude: 116.407526, latitude: 39.904030 }
    }
  }

  // 获取当前位置（使用浏览器地理定位API）
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('浏览器不支持地理定位'))
        return
      }

      const options = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5分钟缓存
      }

      navigator.geolocation.getCurrentPosition(
        position => {
          resolve({
            longitude: position.coords.longitude,
            latitude: position.coords.latitude,
            accuracy: position.coords.accuracy
          })
        },
        error => {
          let message = '获取位置失败'
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = '用户拒绝了地理定位请求'
              break
            case error.POSITION_UNAVAILABLE:
              message = '位置信息不可用'
              break
            case error.TIMEOUT:
              message = '获取位置超时'
              break
          }
          reject(new Error(message))
        },
        options
      )
    })
  }

  // ==================== 菜单API方法 ====================

  // 1. 获取菜单分类
  async getMenuCategories(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = `/api/mobile/menu/categories${queryString ? `?${queryString}` : ''}`
    return this.menuRequest(endpoint)
  }

  // 2. 获取餐品列表
  async getDishes(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = `/api/mobile/menu/dishes${queryString ? `?${queryString}` : ''}`
    return this.menuRequest(endpoint)
  }

  // 3. 获取餐品详情
  async getDishDetail(dishId) {
    return this.menuRequest(`/api/mobile/menu/dishes/${dishId}`)
  }

  // 4. 获取推荐餐品
  async getRecommendedDishes(limit = 10) {
    return this.menuRequest(`/api/mobile/menu/recommended?limit=${limit}`)
  }

  // 5. 搜索餐品
  async searchDishes(keyword, page = 1, limit = 20) {
    const params = new URLSearchParams({
      keyword: keyword,
      page: page.toString(),
      limit: limit.toString()
    })
    return this.menuRequest(`/api/mobile/menu/search?${params}`)
  }

  // 便捷方法：根据分类获取餐品
  async getDishesByCategory(categoryId, page = 1, limit = 20) {
    return this.getDishes({
      category_id: categoryId,
      page: page,
      limit: limit
    })
  }

  // 便捷方法：获取素食餐品
  async getVegetarianDishes(page = 1, limit = 20) {
    return this.getDishes({
      is_vegetarian: 1,
      page: page,
      limit: limit
    })
  }

  // 便捷方法：获取推荐餐品
  async getRecommendedDishesByCategory(categoryId, limit = 5) {
    return this.getDishes({
      category_id: categoryId,
      is_recommended: 1,
      limit: limit
    })
  }

  // ==================== 订单API方法 ====================

  // 1. 创建订单
  async createOrder(orderData) {
    return this.menuRequest('/api/mobile/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    })
  }

  // 2. 获取订单详情
  async getOrderDetail(orderId) {
    return this.menuRequest(`/api/mobile/orders/${orderId}`)
  }

  // 3. 获取用户订单列表
  async getUserOrders(customerPhone, status = '', page = 1, limit = 10) {
    const params = new URLSearchParams({
      customer_phone: customerPhone,
      page: page.toString(),
      limit: limit.toString()
    })
    if (status) params.append('status', status)

    return this.menuRequest(`/api/mobile/orders?${params}`)
  }

  // 4. 取消订单
  async cancelOrder(orderId, cancelReason = '用户取消') {
    return this.menuRequest(`/api/mobile/orders/${orderId}/cancel`, {
      method: 'PUT',
      body: JSON.stringify({ cancel_reason: cancelReason })
    })
  }

  // 5. 获取订单状态
  async getOrderStatus(orderId) {
    try {
      return await this.menuRequest(`/api/mobile/orders/${orderId}/status`)
    } catch (error) {
      console.warn(`⚠️ 订单状态接口失败，尝试使用订单详情接口: ${error.message}`)

      // 降级方案：使用订单详情接口获取状态
      try {
        const orderDetail = await this.getOrderDetail(orderId)
        return {
          id: orderDetail.id,
          order_no: orderDetail.order_no,
          status: orderDetail.status,
          status_text: orderDetail.status_text,
          payment_status: orderDetail.payment_status,
          payment_status_text: orderDetail.payment_status_text,
          progress: orderDetail.progress || 0,
          total_amount: orderDetail.total_amount,
          paid_amount: orderDetail.paid_amount || 0
        }
      } catch (detailError) {
        console.error(`❌ 订单详情接口也失败: ${detailError.message}`)
        throw new Error(`无法获取订单状态: ${error.message}`)
      }
    }
  }

  // 便捷方法：轮询订单状态
  pollOrderStatus(orderId, callback, interval = 30000) {
    const poll = async () => {
      try {
        const status = await this.getOrderStatus(orderId)
        callback(null, status)

        // 如果订单未完成，继续轮询
        if (!['completed', 'cancelled', 'refunded'].includes(status.status)) {
          setTimeout(poll, interval)
        }
      } catch (error) {
        callback(error, null)
        // 出错时延长轮询间隔
        setTimeout(poll, interval * 2)
      }
    }

    poll()
  }

  // 便捷方法：获取活跃订单（未完成的订单）
  async getActiveOrders(customerPhone) {
    const activeStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'serving']
    const allOrders = []

    for (const status of activeStatuses) {
      try {
        const orders = await this.getUserOrders(customerPhone, status, 1, 50)
        if (orders && orders.list) {
          allOrders.push(...orders.list)
        }
      } catch (error) {
        console.warn(`获取${status}状态订单失败:`, error.message)
      }
    }

    return allOrders
  }
}

// 创建单例实例
const apiService = new ApiService()

export default apiService

// 导出具体的API方法供组件使用
export const {
  // 门店相关
  getNearbyStores,
  getStoresList,
  searchStores,
  getStoresByCityCode,
  getStoreDetail,
  getCitiesList,
  getGroupedCities,
  getCityDetail,
  getStoresByCityInfo,
  getStoresByCity,
  getCityCoordinates,
  getCurrentLocation,
  // 菜单相关
  getMenuCategories,
  getDishes,
  getDishDetail,
  getRecommendedDishes,
  searchDishes,
  getDishesByCategory,
  getVegetarianDishes,
  getRecommendedDishesByCategory,
  // 订单相关
  createOrder,
  getOrderDetail,
  getUserOrders,
  cancelOrder,
  getOrderStatus,
  pollOrderStatus,
  getActiveOrders
} = apiService
