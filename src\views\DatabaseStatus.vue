<template>
  <div class="database-status">
    <div class="header">
      <h1>📊 数据库状态监控</h1>
      <p>基于真实API数据的实时状态</p>
      <div class="proxy-status-header">
        <ProxyStatus />
      </div>
    </div>

    <!-- 总览统计 -->
    <div class="overview-section">
      <h2>📈 数据总览</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📋</div>
          <div class="stat-info">
            <div class="stat-number">{{ totalCategories }}</div>
            <div class="stat-label">菜品分类</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🍽️</div>
          <div class="stat-info">
            <div class="stat-number">{{ totalDishes }}</div>
            <div class="stat-label">总菜品数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-number available">{{ availableDishes }}</div>
            <div class="stat-label">可订购</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">❌</div>
          <div class="stat-info">
            <div class="stat-number sold-out">{{ soldOutDishes }}</div>
            <div class="stat-label">已售罄</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类详情 -->
    <div class="categories-section">
      <h2>📋 分类详情</h2>
      <div v-if="loading" class="loading">
        <SvgIcon name="loading" size="24px" color="#007AFF" />
        <span>加载中...</span>
      </div>
      <div v-else-if="categories.length > 0" class="categories-list">
        <div v-for="category in categories" :key="category.id" class="category-item">
          <div class="category-info">
            <h3>{{ category.name }}</h3>
            <p>{{ category.description }}</p>
          </div>
          <div class="category-stats">
            <span class="dish-count">{{ category.dish_count }} 道菜</span>
            <span class="status" :class="category.status ? 'active' : 'inactive'">
              {{ category.status ? '启用' : '禁用' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 菜品详情 -->
    <div class="dishes-section">
      <h2>🍽️ 菜品详情</h2>
      <div v-if="dishes.length > 0" class="dishes-list">
        <div v-for="dish in dishes" :key="dish.id" class="dish-item">
          <div class="dish-info">
            <h3>{{ dish.name }}</h3>
            <p class="dish-category">{{ dish.category_name }}</p>
          </div>
          <div class="dish-status">
            <div class="stock-info">
              <span class="stock-label">库存:</span>
              <span class="stock-value" :class="dish.stock > 0 ? 'in-stock' : 'out-of-stock'">
                {{ dish.stock }}
              </span>
            </div>
            <div class="order-status">
              <span class="status-badge" :class="dish.can_order ? 'available' : 'unavailable'">
                {{ dish.stock_status_text }}
              </span>
            </div>
            <div class="dish-tags">
              <span v-if="dish.is_recommended" class="tag recommended">推荐</span>
              <span v-if="dish.is_spicy" class="tag spicy">{{ dish.spicy_level_text }}</span>
              <span v-if="dish.is_vegetarian" class="tag vegetarian">素食</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问题分析 -->
    <div class="issues-section">
      <h2>⚠️ 发现的问题</h2>
      <div class="issues-list">
        <div class="issue-item critical">
          <SvgIcon name="warning" size="20px" color="#dc3545" />
          <div class="issue-content">
            <h3>所有餐品库存为0</h3>
            <p>导致所有餐品显示"暂时售罄"，无法下单</p>
          </div>
        </div>
        <div class="issue-item warning">
          <SvgIcon name="warning" size="20px" color="#ffc107" />
          <div class="issue-content">
            <h3>缺少图片资源</h3>
            <p>所有餐品和分类都没有图片</p>
          </div>
        </div>
        <div class="issue-item info">
          <SvgIcon name="warning" size="20px" color="#17a2b8" />
          <div class="issue-content">
            <h3>缺少营养信息</h3>
            <p>所有餐品的营养信息为空</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 改进建议 -->
    <div class="suggestions-section">
      <h2>💡 改进建议</h2>
      <div class="suggestions-list">
        <div class="suggestion-item">
          <h3>1. 更新库存数据</h3>
          <pre class="code-block">UPDATE dishes SET stock = 50, min_stock = 10;</pre>
        </div>
        <div class="suggestion-item">
          <h3>2. 设置推荐餐品</h3>
          <pre class="code-block">UPDATE dishes SET is_recommended = 1 WHERE id IN (1,2,4,6);</pre>
        </div>
        <div class="suggestion-item">
          <h3>3. 设置川菜辣度</h3>
          <pre class="code-block">UPDATE dishes SET spicy_level = 2, is_spicy = 1 WHERE category_id = 1;</pre>
        </div>
      </div>
    </div>

    <!-- 刷新按钮 -->
    <div class="actions">
      <button @click="refreshData" :disabled="loading" class="refresh-btn">
        <SvgIcon name="loading" size="16px" color="white" v-if="loading" />
        刷新数据
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import apiService from '@/services/api'
import SvgIcon from '@/components/SvgIcon.vue'
import ProxyStatus from '@/components/ProxyStatus.vue'

export default {
  name: 'DatabaseStatus',
  components: {
    SvgIcon,
    ProxyStatus
  },
  setup() {
    const loading = ref(false)
    const categories = ref([])
    const dishes = ref([])

    // 计算统计数据
    const totalCategories = computed(() => categories.value.length)
    const totalDishes = computed(() => dishes.value.length)
    const availableDishes = computed(() => dishes.value.filter(dish => dish.can_order).length)
    const soldOutDishes = computed(() => dishes.value.filter(dish => !dish.can_order).length)

    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        // 并行加载分类和菜品数据
        const [categoriesData, dishesData] = await Promise.all([
          apiService.getMenuCategories(),
          apiService.getDishes({ limit: 100 })
        ])

        categories.value = categoriesData || []
        dishes.value = dishesData?.list || []

        console.log('📊 数据加载完成:', {
          categories: categories.value.length,
          dishes: dishes.value.length,
          available: availableDishes.value,
          soldOut: soldOutDishes.value
        })
      } catch (error) {
        console.error('❌ 数据加载失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 刷新数据
    const refreshData = () => {
      loadData()
    }

    // 页面加载时获取数据
    onMounted(() => {
      loadData()
    })

    return {
      loading,
      categories,
      dishes,
      totalCategories,
      totalDishes,
      availableDishes,
      soldOutDishes,
      refreshData
    }
  }
}
</script>

<style scoped>
.database-status {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
}

.proxy-status-header {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.overview-section, .categories-section, .dishes-section, .issues-section, .suggestions-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-number.available {
  color: #28a745;
}

.stat-number.sold-out {
  color: #dc3545;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.categories-list, .dishes-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item, .dish-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.category-info h3, .dish-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.category-info p, .dish-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}



.category-stats, .dish-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status.active {
  color: #28a745;
}

.status.inactive {
  color: #dc3545;
}

.stock-value.in-stock {
  color: #28a745;
}

.stock-value.out-of-stock {
  color: #dc3545;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.available {
  background: #d4edda;
  color: #155724;
}

.status-badge.unavailable {
  background: #f8d7da;
  color: #721c24;
}

.dish-tags {
  display: flex;
  gap: 5px;
}

.tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.tag.recommended {
  background: #fff3e0;
  color: #ff9800;
}

.tag.spicy {
  background: #ffebee;
  color: #f44336;
}

.tag.vegetarian {
  background: #e8f5e8;
  color: #4caf50;
}

.issues-list, .suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.issue-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 15px;
  border-radius: 6px;
}

.issue-item.critical {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.issue-item.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
}

.issue-item.info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
}

.issue-content h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.issue-content p {
  margin: 0;
  color: #666;
}

.suggestion-item {
  padding: 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.suggestion-item h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #495057;
  overflow-x: auto;
}

.actions {
  text-align: center;
  margin-top: 20px;
}

.refresh-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
}

.refresh-btn:hover:not(:disabled) {
  background: #0056b3;
}

.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  padding: 20px;
  color: #007AFF;
}
</style>
