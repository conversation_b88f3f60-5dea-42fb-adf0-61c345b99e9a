<template>
  <div class="api-test">
    <div class="header">
      <h1>API测试页面</h1>
      <p>测试菜单API连接状态</p>
    </div>

    <div class="test-section">
      <h2>菜单API测试 - 基于真实数据库</h2>

      <!-- 数据库状态提示 -->
      <div class="database-status">
        <h3>📊 当前数据库状态</h3>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">总分类数:</span>
            <span class="value">9个</span>
          </div>
          <div class="status-item">
            <span class="label">总餐品数:</span>
            <span class="value">9个</span>
          </div>
          <div class="status-item">
            <span class="label">可订购餐品:</span>
            <span class="value error">0个 (全部售罄)</span>
          </div>
          <div class="status-item">
            <span class="label">推荐餐品:</span>
            <span class="value">0个</span>
          </div>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="test-buttons">
        <button @click="testCategories" :disabled="loading">测试分类API</button>
        <button @click="testDishes" :disabled="loading">测试菜品API</button>
        <button @click="testDishDetail" :disabled="loading">测试菜品详情</button>
        <button @click="testSearch" :disabled="loading">测试搜索API</button>
        <button @click="testRecommended" :disabled="loading">测试推荐API</button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <SvgIcon name="loading" size="24px" color="#007AFF" />
        <span>请求中...</span>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error">
        <h3>错误信息：</h3>
        <pre>{{ error }}</pre>
      </div>

      <!-- 成功结果 -->
      <div v-if="result" class="result">
        <h3>API响应结果：</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </div>

    <!-- 代理状态监控 -->
    <div class="proxy-section">
      <h2>🔗 代理状态监控</h2>
      <ProxyStatus />
    </div>

    <!-- API配置信息 -->
    <div class="config-section">
      <h2>⚙️ API配置信息</h2>
      <div class="config-info">
        <p><strong>代理模式：</strong>
          <span class="proxy-mode">开发环境代理</span>
        </p>
        <p><strong>代理目标：</strong> http://localhost:8000</p>
        <p><strong>代理路径：</strong> /api/* → http://localhost:8000/api/*</p>
        <p><strong>当前环境：</strong> {{ currentEnv }}</p>
        <p><strong>连接状态：</strong>
          <span :class="connectionStatus.class">{{ connectionStatus.text }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import apiService from '@/services/api'
import { getCurrentConfig } from '@/config/api'
import SvgIcon from '@/components/SvgIcon.vue'
import ProxyStatus from '@/components/ProxyStatus.vue'

export default {
  name: 'ApiTest',
  components: {
    SvgIcon,
    ProxyStatus
  },
  setup() {
    const loading = ref(false)
    const error = ref(null)
    const result = ref(null)
    const connectionStatus = ref({ class: 'unknown', text: '未知' })

    const config = getCurrentConfig()
    const menuApiUrl = config.MENU_API_BASE_URL
    const currentEnv = process.env.NODE_ENV || 'development'

    // 测试分类API
    const testCategories = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        const data = await apiService.getMenuCategories()
        result.value = data
        connectionStatus.value = { class: 'success', text: '连接正常' }
      } catch (err) {
        error.value = err.message
        connectionStatus.value = { class: 'error', text: '连接失败' }
      } finally {
        loading.value = false
      }
    }

    // 测试菜品API
    const testDishes = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        const data = await apiService.getDishes({ limit: 5 })
        result.value = data
        connectionStatus.value = { class: 'success', text: '连接正常' }

        // 分析数据状态
        if (data && data.list) {
          const availableCount = data.list.filter(dish => dish.can_order).length
          console.log(`📊 数据分析: 共${data.list.length}个菜品，${availableCount}个可订购`)
        }
      } catch (err) {
        error.value = err.message
        connectionStatus.value = { class: 'error', text: '连接失败' }
      } finally {
        loading.value = false
      }
    }

    // 测试菜品详情API
    const testDishDetail = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        // 测试第一个菜品的详情
        const data = await apiService.getDishDetail(1)
        result.value = data
        connectionStatus.value = { class: 'success', text: '连接正常' }
      } catch (err) {
        error.value = err.message
        connectionStatus.value = { class: 'error', text: '连接失败' }
      } finally {
        loading.value = false
      }
    }

    // 测试搜索API
    const testSearch = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        const data = await apiService.searchDishes('鸡', 1, 5)
        result.value = data
        connectionStatus.value = { class: 'success', text: '连接正常' }
      } catch (err) {
        error.value = err.message
        connectionStatus.value = { class: 'error', text: '连接失败' }
      } finally {
        loading.value = false
      }
    }

    // 测试推荐API
    const testRecommended = async () => {
      loading.value = true
      error.value = null
      result.value = null

      try {
        const data = await apiService.getRecommendedDishes(5)
        result.value = data
        connectionStatus.value = { class: 'success', text: '连接正常' }
      } catch (err) {
        error.value = err.message
        connectionStatus.value = { class: 'error', text: '连接失败' }
      } finally {
        loading.value = false
      }
    }

    // 页面加载时测试连接
    onMounted(() => {
      testDishes()
    })

    return {
      loading,
      error,
      result,
      connectionStatus,
      menuApiUrl,
      currentEnv,
      testCategories,
      testDishes,
      testDishDetail,
      testSearch,
      testRecommended
    }
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
}

.test-section, .config-section, .proxy-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.proxy-section {
  border-left: 4px solid #007AFF;
}

.proxy-mode {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.database-status {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
}

.database-status h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.status-item .label {
  font-weight: 500;
  color: #6c757d;
}

.status-item .value {
  font-weight: 600;
  color: #28a745;
}

.status-item .value.error {
  color: #dc3545;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 10px 16px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s;
}

.test-buttons button:hover:not(:disabled) {
  background: #0056b3;
}

.test-buttons button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #007AFF;
  margin: 20px 0;
}

.error {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.error h3 {
  color: #f44336;
  margin: 0 0 10px 0;
}

.error pre {
  color: #d32f2f;
  white-space: pre-wrap;
  word-break: break-word;
}

.result {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.result h3 {
  color: #4caf50;
  margin: 0 0 10px 0;
}

.result pre {
  color: #2e7d32;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.config-info p {
  margin: 10px 0;
}

.config-info strong {
  color: #333;
}

.success {
  color: #4caf50;
  font-weight: 600;
}

.error {
  color: #f44336;
  font-weight: 600;
}

.unknown {
  color: #666;
  font-weight: 600;
}
</style>
