# 代理模式配置说明

## 概述

本项目使用代理模式来访问菜单API，避免跨域问题并提供更好的开发体验。

## 🔗 代理配置

### Vite 代理配置 (`vite.config.js`)

```javascript
export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          // 错误处理
          proxy.on('error', (err, _req, _res) => {
            console.log('🔴 代理错误:', err.message)
          })
          // 请求日志
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('🔗 代理请求:', req.method, req.url)
          })
          // 响应日志
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('📡 代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
```

### 代理规则

| 前端请求 | 代理目标 | 说明 |
|---------|---------|------|
| `http://localhost:3001/api/mobile/menu/categories` | `http://localhost:8000/api/mobile/menu/categories` | 菜单分类API |
| `http://localhost:3001/api/mobile/menu/dishes` | `http://localhost:8000/api/mobile/menu/dishes` | 菜品列表API |
| `http://localhost:3001/api/mobile/menu/search` | `http://localhost:8000/api/mobile/menu/search` | 搜索API |

## 🛠️ API服务配置

### 环境配置 (`src/config/api.js`)

```javascript
export const ENV_CONFIG = {
  development: {
    MENU_API_BASE_URL: '', // 代理模式下为空
    STORE_API_BASE_URL: '/api'
  },
  production: {
    MENU_API_BASE_URL: 'https://api.example.com',
    STORE_API_BASE_URL: '/api'
  }
}
```

### API服务类 (`src/services/api.js`)

```javascript
// 菜单API专用请求方法 - 代理模式
async menuRequest(endpoint, options = {}) {
  // 使用代理模式，直接使用相对路径
  const url = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`
  
  try {
    console.log(`🔗 菜单API请求 (代理): ${url}`)
    const response = await fetch(url, config)
    // ... 处理响应
  } catch (error) {
    console.error('❌ 菜单API请求失败:', error)
    throw error
  }
}
```

## 📊 代理状态监控

### ProxyStatus 组件

项目包含一个代理状态监控组件，实时检查代理连接状态：

- ✅ **连接正常**: 代理工作正常
- ⚠️ **连接断开**: 无法连接到目标服务器
- ❌ **连接错误**: 代理配置或服务器错误

### 使用方法

```vue
<template>
  <ProxyStatus />
</template>

<script>
import ProxyStatus from '@/components/ProxyStatus.vue'
</script>
```

## 🚀 启动说明

### 1. 启动API服务器

确保菜单API服务器在端口8000运行：

```bash
# 启动菜单API服务器
cd /path/to/menu-api
npm start
# 或
python app.py
# 服务器应该在 http://localhost:8000 运行
```

### 2. 启动前端开发服务器

```bash
# 启动前端项目
npm run dev
# 前端服务器在 http://localhost:3001 运行
```

### 3. 验证代理配置

访问以下页面验证代理是否正常工作：

- **API测试页面**: `http://localhost:3001/api-test`
- **数据库状态**: `http://localhost:3001/database-status`
- **菜单页面**: `http://localhost:3001/menu/1`

## 🔍 故障排除

### 常见问题

#### 1. 代理连接失败

**错误信息**: `无法连接到菜单服务器，请检查代理配置`

**解决方案**:
- 确认API服务器在 `http://localhost:8000` 运行
- 检查防火墙设置
- 确认端口8000没有被其他程序占用

#### 2. CORS错误

**错误信息**: `Access to fetch at ... has been blocked by CORS policy`

**解决方案**:
- 确认代理配置中的 `changeOrigin: true`
- 检查API服务器的CORS设置

#### 3. 代理超时

**错误信息**: `代理请求超时`

**解决方案**:
- 增加代理超时时间
- 检查网络连接
- 确认API服务器响应正常

### 调试方法

#### 1. 查看代理日志

开发服务器控制台会显示详细的代理日志：

```
🔗 代理请求: GET /api/mobile/menu/dishes → localhost:8000
📡 代理响应: 200 /api/mobile/menu/dishes
```

#### 2. 使用浏览器开发者工具

- 打开Network面板
- 查看API请求的实际URL
- 检查响应状态和内容

#### 3. 直接测试API

使用curl或Postman直接测试API服务器：

```bash
curl http://localhost:8000/api/mobile/menu/categories
```

## 🌐 生产环境配置

### 反向代理配置 (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://api-server:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Docker Compose 配置

```yaml
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "3001:3001"
    depends_on:
      - api-server
      
  api-server:
    image: menu-api:latest
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
```

## 📈 性能优化

### 1. 代理缓存

```javascript
proxy: {
  '/api': {
    target: 'http://localhost:8000',
    changeOrigin: true,
    // 添加缓存头
    onProxyRes: (proxyRes, req, res) => {
      if (req.url.includes('/categories')) {
        proxyRes.headers['cache-control'] = 'max-age=300'
      }
    }
  }
}
```

### 2. 请求压缩

```javascript
proxy: {
  '/api': {
    target: 'http://localhost:8000',
    changeOrigin: true,
    // 启用压缩
    headers: {
      'Accept-Encoding': 'gzip, deflate'
    }
  }
}
```

## 🔒 安全考虑

### 1. 开发环境安全

- 代理仅在开发环境启用
- 不要在生产环境暴露内部API地址
- 使用环境变量管理敏感配置

### 2. 生产环境安全

- 使用HTTPS
- 配置适当的CORS策略
- 实施API访问限制和认证

## 📝 最佳实践

### 1. 错误处理

```javascript
// 统一的错误处理
async menuRequest(endpoint, options = {}) {
  try {
    const response = await fetch(url, config)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    return await response.json()
  } catch (error) {
    // 记录错误
    console.error('API请求失败:', error)
    // 抛出友好的错误信息
    throw new Error('服务暂时不可用，请稍后重试')
  }
}
```

### 2. 请求重试

```javascript
// 自动重试机制
async requestWithRetry(url, options, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fetch(url, options)
    } catch (error) {
      if (i === retries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

### 3. 监控和日志

```javascript
// 请求监控
proxy.on('proxyReq', (proxyReq, req, res) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`)
})

proxy.on('proxyRes', (proxyRes, req, res) => {
  console.log(`[${new Date().toISOString()}] ${proxyRes.statusCode} ${req.url}`)
})
```

通过以上配置，项目实现了完整的代理模式API访问，提供了良好的开发体验和生产环境支持。
