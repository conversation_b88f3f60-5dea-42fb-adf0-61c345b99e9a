import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import CitySelector from '../views/CitySelector.vue'
import RestaurantDetail from '../views/RestaurantDetail.vue'
import MenuPage from '../views/MenuPage.vue'
import OrderConfirm from '../views/OrderConfirm.vue'
import OrderStatus from '../views/OrderStatus.vue'
import OrdersList from '../views/OrdersList.vue'
import OrderProcessing from '../views/OrderProcessing.vue'
import OrderSuccess from '../views/OrderSuccess.vue'
import BusinessHoursTest from '../views/BusinessHoursTest.vue'
import ApiTest from '../views/ApiTest.vue'
import DatabaseStatus from '../views/DatabaseStatus.vue'
import OrderApiTest from '../views/OrderApiTest.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/city',
    name: 'CitySelector',
    component: CitySelector
  },
  {
    path: '/restaurant/:id',
    name: 'RestaurantDetail',
    component: RestaurantDetail
  },
  {
    path: '/menu/:id',
    name: 'MenuPage',
    component: MenuPage
  },
  {
    path: '/order/confirm/:restaurantId',
    name: 'OrderConfirm',
    component: OrderConfirm
  },
  {
    path: '/order/status/:orderNumber',
    name: 'OrderStatus',
    component: OrderStatus
  },
  {
    path: '/order/processing/:orderNumber',
    name: 'OrderProcessing',
    component: OrderProcessing
  },
  {
    path: '/order/success/:orderNumber',
    name: 'OrderSuccess',
    component: OrderSuccess
  },
  {
    path: '/orders',
    name: 'OrdersList',
    component: OrdersList
  },
  {
    path: '/business-hours-test',
    name: 'BusinessHoursTest',
    component: BusinessHoursTest
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: ApiTest
  },
  {
    path: '/database-status',
    name: 'DatabaseStatus',
    component: DatabaseStatus
  },
  {
    path: '/order-api-test',
    name: 'OrderApiTest',
    component: OrderApiTest
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 从城市选择页面返回时，滚动到顶部
    if (from.name === 'CitySelector') {
      return { top: 0, behavior: 'smooth' }
    }
    
    // 其他情况保持默认行为
    if (savedPosition) {
      return savedPosition
    }
    
    return { top: 0 }
  }
})

export default router
