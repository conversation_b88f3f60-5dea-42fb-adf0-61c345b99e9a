<template>
  <div class="order-processing">
    <!-- 顶部导航 -->
    <div class="header">
      <h1>订单处理中</h1>
    </div>

    <!-- 处理状态 -->
    <div class="processing-section">
      <div class="processing-icon">
        <div class="spinner" v-if="!isCompleted">
          <SvgIcon name="loading" size="48px" color="#007AFF" />
        </div>
        <div class="success-icon" v-else>
          <SvgIcon name="success" size="48px" color="#4caf50" />
        </div>
      </div>
      
      <h2 v-if="!isCompleted">正在处理您的订单...</h2>
      <h2 v-else>订单处理完成！</h2>
      
      <div class="processing-steps">
        <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <SvgIcon name="document" size="20px" color="currentColor" />
          <span class="step-text">确认订单信息</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <SvgIcon name="restaurant" size="20px" color="currentColor" />
          <span class="step-text">发送到餐厅</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
          <SvgIcon name="chef" size="20px" color="currentColor" />
          <span class="step-text">餐厅确认接单</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
          <SvgIcon name="ticket" size="20px" color="currentColor" />
          <span class="step-text">生成出餐号</span>
        </div>
      </div>

      <div class="processing-message">
        <p v-if="currentStep === 1">正在确认订单信息...</p>
        <p v-else-if="currentStep === 2">正在发送订单到餐厅...</p>
        <p v-else-if="currentStep === 3">等待餐厅确认接单...</p>
        <p v-else-if="currentStep === 4">正在生成出餐号...</p>
        <p v-else-if="isCompleted">订单已成功提交，正在跳转...</p>
      </div>
    </div>

    <!-- 订单信息预览 -->
    <div class="order-preview" v-if="orderItems.length > 0">
      <h3>订单详情</h3>
      <div class="preview-items">
        <div v-for="item in orderItems" :key="item.id" class="preview-item">
          <span class="item-name">{{ item.name }}</span>
          <span class="item-status">等待处理</span>
        </div>
      </div>
      <div class="preview-total">
        共 {{ orderItems.length }} 道菜品
      </div>
    </div>

    <!-- 温馨提示 -->
    <div class="tips-section">
      <h3>温馨提示</h3>
      <ul>
        <li>订单处理通常需要1-3分钟</li>
        <li>处理完成后将自动跳转到订单状态页面</li>
        <li>您将获得每道菜品的独立出餐号</li>
        <li>请耐心等待，不要重复提交订单</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'
import SvgIcon from '@/components/SvgIcon.vue'

export default {
  name: 'OrderProcessing',
  components: {
    SvgIcon
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const ordersStore = useOrdersStore()
    
    const orderNumber = route.params.orderNumber
    const currentStep = ref(1)
    const isCompleted = ref(false)
    const orderItems = ref([])
    
    let processingInterval = null

    const processOrder = async () => {
      try {
        // 获取订单信息
        const order = ordersStore.getOrder(orderNumber)
        if (order) {
          orderItems.value = order.items
        }

        // 步骤1: 确认订单信息 (1秒)
        currentStep.value = 1
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 步骤2: 发送到餐厅 (1.5秒)
        currentStep.value = 2
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 步骤3: 餐厅确认接单 (2秒)
        currentStep.value = 3
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 步骤4: 生成出餐号 (1秒)
        currentStep.value = 4
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 处理完成
        isCompleted.value = true

        // 更新订单状态，添加出餐号
        if (order) {
          const updatedItems = order.items.map(item => ({
            ...item,
            mealNumber: generateMealNumber(),
            status: 'cooking' // 开始制作
          }))

          ordersStore.updateOrderItems(orderNumber, updatedItems)
          ordersStore.updateOrderStatus(orderNumber, { orderStatus: 'confirmed' })
        }

        // 等待1秒后跳转
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 跳转到订单状态页面
        router.replace({
          name: 'OrderStatus',
          params: { orderNumber }
        })

      } catch (error) {
        console.error('订单处理失败:', error)
        alert('订单处理失败，请重试')
        router.go(-1)
      }
    }

    const generateMealNumber = () => {
      return String(Math.floor(Math.random() * 900) + 100)
    }

    onMounted(() => {
      // 初始化订单管理
      ordersStore.init()
      
      // 开始处理订单
      processOrder()
    })

    onUnmounted(() => {
      if (processingInterval) {
        clearInterval(processingInterval)
      }
    })

    return {
      orderNumber,
      currentStep,
      isCompleted,
      orderItems
    }
  }
}
</script>

<style scoped>
.order-processing {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 20px;
}

.header {
  text-align: center;
  padding: 20px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.processing-section {
  background: white;
  padding: 40px 20px;
  margin: 16px;
  border-radius: 12px;
  text-align: center;
}

.processing-icon {
  margin-bottom: 20px;
}

.spinner {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.processing-section h2 {
  margin: 0 0 30px 0;
  font-size: 18px;
  color: #333;
}

.processing-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 30px;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s;
}

.step.active {
  background: #e3f2fd;
  border: 1px solid #2196f3;
}

.step.completed {
  background: #e8f5e8;
  border: 1px solid #4caf50;
}



.step-text {
  font-size: 14px;
  color: #333;
}

.processing-message {
  color: #666;
  font-size: 14px;
}

.order-preview {
  background: white;
  padding: 16px;
  margin: 16px;
  border-radius: 8px;
}

.order-preview h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preview-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 14px;
}

.item-status {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.preview-total {
  text-align: center;
  font-weight: 600;
  color: #333;
  padding-top: 16px;
  border-top: 2px solid #f0f0f0;
}

.tips-section {
  background: white;
  padding: 16px;
  margin: 16px;
  border-radius: 8px;
}

.tips-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.tips-section ul {
  margin: 0;
  padding-left: 20px;
}

.tips-section li {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}
</style>
