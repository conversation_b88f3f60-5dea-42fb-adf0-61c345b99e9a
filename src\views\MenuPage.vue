<template>
  <div class="menu-page">
    <!-- 顶部导航 -->
    <div class="header">
      <button class="back-btn" @click="goBack">←</button>
      <h1>{{ restaurant.name || '餐厅菜单' }}</h1>
      <button class="orders-btn" @click="goToOrders" v-if="hasOrders">
        <span class="orders-icon">📋</span>
        <span class="orders-count">{{ orderCount }}</span>
      </button>
    </div>
    
    <!-- 餐厅信息 -->
    <div class="restaurant-info" v-if="restaurant">
      <div class="restaurant-header">
        <h2>{{ restaurant.name }}</h2>
        <div class="restaurant-meta">
          <span class="status" :class="businessStatus.className">
            {{ businessStatus.text }}
          </span>
          <span class="hours">{{ restaurant.start_time }}-{{ restaurant.end_time }}</span>
        </div>
      </div>
      <p class="restaurant-address">{{ restaurant.address }}</p>
    </div>



    <!-- 菜品列表 -->
    <div class="menu-content">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">🔄</div>
        <p>正在加载菜单...</p>
      </div>

      <div v-else-if="error" class="error-state">
        <p>{{ error }}</p>
        <button @click="loadMenu" class="retry-btn">重试</button>
      </div>

      <div v-else-if="menuItems.length > 0" class="menu-list">
        <div
          v-for="item in menuItems"
          :key="item.id"
          class="menu-item"
        >
          <div class="item-image">
            <img :src="item.image || '/placeholder-food.jpg'" :alt="item.name">
          </div>
          <div class="item-info">
            <h3 class="item-name">{{ item.name }}</h3>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-meta">
              <span class="item-sales" v-if="item.sales > 0">已售{{ item.sales }}</span>
            </div>
            <div class="item-status" v-if="!item.can_order">
              <span class="stock-status out-of-stock">{{ item.stock_status_text || '暂时售罄' }}</span>
            </div>
          </div>
          <div class="item-actions">
            <!-- 售罄状态 -->
            <div v-if="!item.can_order" class="sold-out-section">
              <button class="sold-out-btn" disabled>
                {{ item.stock_status_text || '暂时售罄' }}
              </button>
            </div>
            <!-- 可点餐状态 -->
            <div v-else-if="getItemQuantity(item.id) === 0" class="add-section">
              <button
                class="add-btn"
                @click="addToCart(item)"
                :disabled="!businessStatus.isOpen"
              >
                {{ businessStatus.isOpen ? '点餐' : '已打烊' }}
              </button>
            </div>
            <!-- 数量控制 -->
            <div v-else class="quantity-control">
              <button
                class="quantity-btn minus"
                @click="decreaseQuantity(item.id)"
              >
                <SvgIcon name="remove" size="16px" color="white" />
              </button>
              <span class="quantity-display">{{ getItemQuantity(item.id) }}</span>
              <button
                class="quantity-btn plus"
                @click="increaseQuantity(item)"
                :disabled="!businessStatus.isOpen || getItemQuantity(item.id) >= item.maxQuantity || !item.can_order"
              >
                <SvgIcon name="add" size="16px" color="white" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="no-menu-state">
        <div class="no-menu-icon">🍽️</div>
        <p>暂无菜品信息</p>
      </div>
    </div>

    <!-- 购物车悬浮按钮 -->
    <div class="cart-float" @click="showCart = true" :class="{ 'cart-empty': cartItems.length === 0 }">
      <SvgIcon name="cart" size="20px" color="white" />
      <span class="cart-count" v-if="cartItems.length > 0">{{ cartItems.length }}</span>
      <span class="cart-text">{{ cartItems.length > 0 ? '已选菜品' : '选择菜品' }}</span>
    </div>

    <!-- 购物车弹窗 -->
    <div class="cart-modal" v-if="showCart" @click="showCart = false">
      <div class="cart-content" @click.stop>
        <div class="cart-header">
          <h3>已选菜品</h3>
          <button class="close-btn" @click="showCart = false">
          <SvgIcon name="close" size="20px" color="#666" />
        </button>
        </div>
        <div class="cart-items">
          <div v-if="cartItems.length === 0" class="cart-empty-state">
            <SvgIcon name="food" size="48px" color="#ccc" />
            <p>还没有选择菜品</p>
            <p class="empty-tip">浏览菜单，点击"点餐"按钮添加菜品</p>
          </div>
          <div v-else>
            <div v-for="group in groupedCartItems" :key="group.menu_item_id" class="cart-item">
              <div class="item-info">
                <span class="item-name">{{ group.name }}</span>
                <span class="item-quantity">× {{ group.quantity }}</span>
              </div>
              <div class="item-controls">
                <button class="quantity-btn minus" @click="decreaseQuantity(group.menu_item_id)">
                  <SvgIcon name="remove" size="16px" color="white" />
                </button>
                <span class="quantity-display">{{ group.quantity }}</span>
                <button class="quantity-btn plus" @click="increaseQuantity(getMenuItemById(group.menu_item_id))">
                  <SvgIcon name="add" size="16px" color="white" />
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="cart-footer">
          <div class="cart-total-info">
            {{ cartItems.length > 0 ? `共 ${cartItems.length} 道菜品` : '购物车为空' }}
          </div>
          <button class="checkout-btn" @click="checkout" :disabled="cartItems.length === 0">
            {{ cartItems.length > 0 ? '确认点餐' : '请先选择菜品' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'
import apiService from '@/services/api'
import { getBusinessStatus, createBusinessStatusWatcher } from '@/utils/businessHours'
import { maskPhone } from '@/utils/phoneUtils'
import SvgIcon from '@/components/SvgIcon.vue'

export default {
  name: 'MenuPage',
  components: {
    SvgIcon
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const ordersStore = useOrdersStore()

    const restaurantId = route.params.id
    const loading = ref(true)
    const error = ref(null)
    const restaurant = ref({})
    const menuItems = ref([])
    const cartItems = ref([])
    const showCart = ref(false)
    const businessStatus = ref({ isOpen: true, status: 'open', text: '营业中' })

    let statusWatcher = null

    // 计算属性

    const cartTotal = computed(() => {
      return cartItems.value.length
    })

    // 获取分组后的购物车项目
    const groupedCartItems = computed(() => {
      const groups = {}
      cartItems.value.forEach(item => {
        if (!groups[item.menu_item_id]) {
          groups[item.menu_item_id] = {
            menu_item_id: item.menu_item_id,
            name: item.name,
            phone: item.phone,
            quantity: 0,
            items: []
          }
        }
        groups[item.menu_item_id].quantity++
        groups[item.menu_item_id].items.push(item)
      })
      return Object.values(groups)
    })

    const hasOrders = computed(() => {
      return ordersStore.activeOrdersCount > 0
    })

    const orderCount = computed(() => {
      return ordersStore.activeOrdersCount
    })

    // 方法
    const goBack = () => {
      router.go(-1)
    }

    const goToOrders = () => {
      router.push('/orders')
    }



    // 获取菜品在购物车中的数量
    const getItemQuantity = (itemId) => {
      return cartItems.value.filter(cartItem => cartItem.menu_item_id === itemId).length
    }

    // 添加到购物车
    const addToCart = (item) => {
      const currentQuantity = getItemQuantity(item.id)
      if (currentQuantity < item.maxQuantity) {
        cartItems.value.push({
          id: Date.now() + Math.random(), // 确保唯一ID
          menu_item_id: item.id,
          name: item.name,
          phone: item.phone,
          restaurant_id: restaurantId
        })
      }
    }

    // 增加数量
    const increaseQuantity = (item) => {
      addToCart(item)
    }

    // 减少数量
    const decreaseQuantity = (itemId) => {
      const index = cartItems.value.findIndex(cartItem => cartItem.menu_item_id === itemId)
      if (index > -1) {
        cartItems.value.splice(index, 1)
      }
    }

    // 根据ID获取菜品信息
    const getMenuItemById = (itemId) => {
      return menuItems.value.find(item => item.id === itemId)
    }

    // 为菜品生成手机号的辅助函数
    const generatePhoneForDish = (dishId) => {
      const phones = [
        '13812345678', '13987654321', '***********', '18612345678',
        '13712345678', '15987654321', '18987654321', '13612345678'
      ]
      return phones[(dishId - 1) % phones.length]
    }

    const removeFromCart = (cartItemId) => {
      const index = cartItems.value.findIndex(item => item.id === cartItemId)
      if (index > -1) {
        cartItems.value.splice(index, 1)
      }
    }

    const checkout = () => {
      if (cartItems.value.length === 0) return

      // 跳转到订单确认页面，通过query参数传递数据
      const cartData = JSON.stringify(cartItems.value)
      const restaurantData = JSON.stringify(restaurant.value)
      router.push({
        name: 'OrderConfirm',
        params: { restaurantId },
        query: { cartData, restaurant: restaurantData }
      })
    }

    // 更新营业状态
    const updateBusinessStatus = () => {
      if (restaurant.value.start_time && restaurant.value.end_time) {
        businessStatus.value = getBusinessStatus(
          restaurant.value.start_time,
          restaurant.value.end_time
        )

        // 创建状态监听器
        if (statusWatcher) {
          statusWatcher.destroy()
        }

        statusWatcher = createBusinessStatusWatcher(
          restaurant.value.start_time,
          restaurant.value.end_time
        )

        statusWatcher.onStatusChange((newStatus) => {
          businessStatus.value = newStatus
        })
      }
    }

    const loadMenu = async () => {
      loading.value = true
      error.value = null

      try {
        // 首先获取餐厅详情
        try {
          const storeData = await apiService.getStoreDetail(restaurantId)

          // 设置真实的餐厅数据
          if (storeData) {
            restaurant.value = {
              id: storeData.id || restaurantId,
              name: storeData.name || '餐厅',
              shop_code: storeData.shop_code || storeData.code || `SHOP_${restaurantId}`, // 店铺编号
              address: storeData.address || '',
              start_time: storeData.start_time || '09:00',
              end_time: storeData.end_time || '22:00',
              phone: storeData.phone || ''
            }

            // 根据营业时间计算营业状态
            updateBusinessStatus()
          } else {
            throw new Error('餐厅数据为空')
          }
        } catch (apiError) {
          console.warn('获取餐厅详情失败，使用默认数据:', apiError.message)
          // 如果API调用失败，使用默认值
          restaurant.value = {
            id: restaurantId,
            name: `餐厅 #${restaurantId}`,
            shop_code: `SHOP_${restaurantId}`, // 默认店铺编号
            address: '地址信息暂无',
            start_time: '09:00',
            end_time: '22:00',
            phone: ''
          }

          // 根据营业时间计算营业状态
          updateBusinessStatus()
        }

        // 从真实API获取菜品数据 - 基于真实数据库
        try {
          console.log('🍽️ 开始获取菜品数据...')
          const dishesData = await apiService.getDishes({
            limit: 50 // 获取所有菜品
          })

          console.log('📊 API返回的菜品数据:', dishesData)

          // 处理真实API返回的数据结构
          if (dishesData && dishesData.list && Array.isArray(dishesData.list)) {
            menuItems.value = dishesData.list.map(dish => ({
              // 基础信息
              id: dish.id,
              name: dish.name,
              description: dish.description || `${dish.category_name}经典菜品`, // 如果没有描述，生成默认描述

              // 图片信息
              image: dish.main_image || '', // 主图片
              images: dish.images || [], // 所有图片

              // 分类信息
              category_id: dish.category_id,
              category_name: dish.category_name,

              // 库存和订购信息
              stock: dish.stock,
              maxQuantity: Math.max(dish.stock, 0), // 使用真实库存，最小为0
              stock_status: dish.stock_status,
              stock_status_text: dish.stock_status_text,
              can_order: dish.can_order,

              // 菜品特性
              dish_type: dish.dish_type,
              dish_type_text: dish.dish_type_text,
              spicy_level: dish.spicy_level,
              spicy_level_text: dish.spicy_level_text,
              is_spicy: dish.is_spicy,
              is_vegetarian: dish.is_vegetarian,
              is_recommended: dish.is_recommended,

              // 其他信息
              sales: dish.sales,
              cooking_time: dish.cooking_time,
              nutrition: dish.nutrition,
              ingredients: dish.ingredients || [],
              allergens: dish.allergens || [],
              tags: dish.tags || [],

              // 生成的手机号（出餐后显示）
              phone: generatePhoneForDish(dish.id)
            }))

            console.log(`✅ 成功加载 ${menuItems.value.length} 个菜品`)

            // 统计可订购菜品数量
            const availableCount = menuItems.value.filter(item => item.can_order).length
            console.log(`📈 可订购菜品: ${availableCount}/${menuItems.value.length}`)

          } else {
            throw new Error('API返回数据格式错误')
          }
        } catch (menuError) {
          console.error('❌ 获取菜品数据失败:', menuError.message)
          error.value = `获取菜品失败: ${menuError.message}`

          // API失败时使用模拟数据（模拟真实API的数据结构）
          console.log('🔄 使用模拟数据...')
          menuItems.value = [
            {
              id: 1, name: '宫保鸡丁', description: '经典川菜，鸡肉嫩滑，花生香脆',
              category_name: '川菜', stock: 0, maxQuantity: 0,
              can_order: false, stock_status: 'out_of_stock', stock_status_text: '暂时售罄',
              phone: '13812345678', sales: 0
            },
            {
              id: 2, name: '麻婆豆腐', description: '传统川菜，豆腐嫩滑，麻辣鲜香',
              category_name: '川菜', stock: 0, maxQuantity: 0,
              can_order: false, stock_status: 'out_of_stock', stock_status_text: '暂时售罄',
              phone: '13987654321', sales: 0
            },
            {
              id: 3, name: '回锅肉', description: '四川名菜，肥而不腻，香辣下饭',
              category_name: '川菜', stock: 0, maxQuantity: 0,
              can_order: false, stock_status: 'out_of_stock', stock_status_text: '暂时售罄',
              phone: '***********', sales: 0
            }
          ]
        }

      } catch (err) {
        error.value = '加载菜单失败: ' + err.message
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      // 尝试从历史状态获取餐厅数据
      const historyState = history.state
      if (historyState && historyState.restaurant) {
        restaurant.value = historyState.restaurant
        updateBusinessStatus()
      }

      loadMenu()
    })

    onUnmounted(() => {
      // 清理状态监听器
      if (statusWatcher) {
        statusWatcher.destroy()
      }
    })

    return {
      restaurant,
      menuItems,
      cartItems,
      showCart,
      loading,
      error,
      businessStatus,
      cartTotal,
      groupedCartItems,
      hasOrders,
      orderCount,
      goBack,
      goToOrders,
      getItemQuantity,
      addToCart,
      increaseQuantity,
      decreaseQuantity,
      getMenuItemById,
      removeFromCart,
      checkout,
      loadMenu,
      updateBusinessStatus,
      maskPhone
    }
  }
}
</script>

<style scoped>
.menu-page {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  flex: 1;
  text-align: center;
}

.orders-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #007AFF;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
}

.orders-count {
  background: #ff3b30;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  min-width: 16px;
  text-align: center;
}

/* 餐厅信息 */
.restaurant-info {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.restaurant-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.restaurant-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.open {
  background: #e8f5e8;
  color: #4caf50;
}

.status.closed {
  background: #ffeaea;
  color: #f44336;
}

.hours {
  color: #666;
  font-size: 14px;
}

.restaurant-address {
  color: #666;
  font-size: 14px;
  margin: 0;
}



/* 菜品列表 */
.menu-content {
  flex: 1;
}

.loading-state, .error-state, .no-menu-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
}

.loading-spinner {
  font-size: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.retry-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  margin-top: 16px;
}

.menu-list {
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.item-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.item-sales {
  color: #999;
  font-size: 12px;
}

.item-status {
  margin-top: 8px;
}

.stock-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stock-status.out-of-stock {
  background: #ffebee;
  color: #f44336;
  border: 1px solid #f44336;
}

.sold-out-section {
  display: flex;
  align-items: center;
}

.sold-out-btn {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: not-allowed;
}





.item-actions {
  display: flex;
  align-items: center;
  margin-left: 12px;
  min-width: 120px;
  justify-content: flex-end;
}

.add-section {
  display: flex;
  align-items: center;
}

.add-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.add-btn:hover:not(:disabled) {
  background: #0056b3;
}

.add-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f8ff;
  border: 1px solid #007AFF;
  border-radius: 20px;
  padding: 4px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: #007AFF;
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.quantity-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: scale(1.1);
}

.quantity-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.quantity-btn.minus {
  background: #ff6b35;
}

.quantity-btn.minus:hover:not(:disabled) {
  background: #e55a2b;
}

.quantity-display {
  min-width: 20px;
  text-align: center;
  font-weight: 600;
  color: #007AFF;
}

/* 购物车悬浮按钮 */
.cart-float {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #007AFF;
  color: white;
  padding: 12px 16px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  z-index: 1000;
  transition: all 0.3s;
}

.cart-float.cart-empty {
  background: #6c757d;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.cart-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.cart-float.cart-empty:hover {
  box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
}

.cart-count {
  background: #ff3b30;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  min-width: 18px;
  text-align: center;
}

/* 购物车弹窗 */
.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 2000;
}

.cart-content {
  background: white;
  width: 100%;
  max-height: 70vh;
  border-radius: 16px 16px 0 0;
  display: flex;
  flex-direction: column;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.cart-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.cart-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}



.cart-empty-state p {
  margin: 8px 0;
  font-size: 16px;
}

.empty-tip {
  font-size: 14px !important;
  color: #999 !important;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.item-quantity {
  font-size: 12px;
  color: #666;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-controls .quantity-btn {
  width: 24px;
  height: 24px;
  font-size: 14px;
}

.item-controls .quantity-display {
  min-width: 16px;
  font-size: 14px;
}

.cart-text {
  font-size: 12px;
}

.remove-btn {
  background: #ff3b30;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
}

.cart-footer {
  padding: 16px;
  border-top: 1px solid #eee;
}

.cart-total-info {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.checkout-btn {
  width: 100%;
  background: #007AFF;
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
}

.checkout-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
