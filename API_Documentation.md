# 门店API文档

## 概述

本文档描述了门店相关的API接口，主要用于查询门店信息、搜索附近门店等功能。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方法**: GET

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据内容
    }
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API接口列表

### 1. 附近门店搜索 (推荐)

**接口地址**: `/api/stores/nearby`

**请求方法**: GET

**功能描述**: 
- 智能搜索附近门店，优先返回最近门店所在城市的所有门店
- 确保返回结果的一致性，避免跨城市混合结果

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| longitude | string | 是 | - | 经度 |
| latitude | string | 是 | - | 纬度 |
| city_only | string/int | 否 | true | 是否只返回同城门店 (true/1: 是, false/0: 否) |
| radius | int | 否 | 50 | 搜索半径(公里) |
| limit | int | 否 | 0 | 返回门店数量限制 (0=不限制，返回所有门店) |
| status | string | 否 | - | 门店状态筛选 |

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "location": {
            "longitude": "115.10888",
            "latitude": "33.39519"
        },
        "current_city": {
            "city_code": "410100",
            "city_name": "郑州市"
        },
        "nearest_store": {
            "name": "郑州中原万达店",
            "distance": 2.5,
            "distance_text": "2.5公里",
            "city_code": "410100"
        },
        "stores": [
            {
                "id": 1,
                "store_code": "ZZ001",
                "name": "郑州中原万达店",
                "address": "河南省郑州市中原区中原万达广场",
                "phone": "0371-12345678",
                "longitude": "113.625368",
                "latitude": "34.746599",
                "city_code": "410100",
                "city_name": "郑州市",
                "distance": 2.5,
                "distance_text": "2.5公里",
                "status": "normal",
                "start_time": "09:00",
                "end_time": "22:00"
            }
        ],
        "debug_info": {
            "version": "THINKPHP_V4.0",
            "method": "thinkphp_query_builder",
            "logic_steps": [
                "1. 找到最近门店: 郑州中原万达店",
                "2. 确定目标城市: 郑州市 (410100)",
                "3. 筛选该城市门店: 5个",
                "4. 按距离排序: 完成"
            ],
            "city_code_consistent": true
        }
    }
}
```

### 2. 门店列表

**接口地址**: `/api/stores`

**请求方法**: GET

**功能描述**: 获取所有门店列表

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| limit | int | 否 | 20 | 每页数量 |
| status | string | 否 | - | 门店状态筛选 |

### 3. 按城市搜索门店

**接口地址**: `/api/stores/search`

**请求方法**: GET

**功能描述**: 根据城市名称或城市代码搜索门店

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| city | string | 否 | - | 城市名称 |
| city_code | string | 否 | - | 城市代码 |
| keyword | string | 否 | - | 关键词搜索 |

### 4. 根据城市获取门店

**接口地址**: `/api/stores/by-city/{city_code}`

**请求方法**: GET

**功能描述**: 根据城市代码获取该城市的所有门店

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| city_code | string | 是 | 城市代码 |

### 5. 门店详情

**接口地址**: `/api/stores/{store_id}`

**请求方法**: GET

**功能描述**: 获取指定门店的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| store_id | int | 是 | 门店ID |

## 城市API接口

### 6. 获取所有城市列表

**接口地址**: `/api/cities`

**请求方法**: GET

**功能描述**: 获取所有城市列表，支持按首字母筛选和关键词搜索

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| first_char | string | 否 | - | 首字母筛选 (A-Z) |
| keyword | string | 否 | - | 关键词搜索 (城市名称或拼音) |

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "city_code": "110100",
            "city_name": "北京市",
            "city_pinyin": "beijing",
            "first_char": "B",
            "province": "北京市",
            "level": 1
        },
        {
            "city_code": "310100",
            "city_name": "上海市",
            "city_pinyin": "shanghai",
            "first_char": "S",
            "province": "上海市",
            "level": 1
        }
    ]
}
```

### 7. 获取首字母分组城市

**接口地址**: `/api/cities/grouped`

**请求方法**: GET

**功能描述**: 获取按首字母分组的城市列表，适用于城市选择器

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "A": [
            {
                "city_code": "340100",
                "city_name": "合肥市",
                "city_pinyin": "hefei"
            }
        ],
        "B": [
            {
                "city_code": "110100",
                "city_name": "北京市",
                "city_pinyin": "beijing"
            }
        ],
        "S": [
            {
                "city_code": "310100",
                "city_name": "上海市",
                "city_pinyin": "shanghai"
            }
        ]
    }
}
```

### 8. 获取城市详情

**接口地址**: `/api/cities/{city_code}`

**请求方法**: GET

**功能描述**: 根据城市代码获取城市详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| city_code | string | 是 | 城市代码 (如: 110100) |

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "city_code": "110100",
        "city_name": "北京市",
        "city_pinyin": "beijing",
        "first_char": "B",
        "province": "北京市",
        "level": 1
    }
}
```

## 调用示例

### JavaScript/Ajax 调用

```javascript
// 1. 搜索附近门店 (推荐用法)
$.ajax({
    url: 'http://localhost:8000/api/stores/nearby',
    method: 'GET',
    data: {
        longitude: '115.10888',
        latitude: '33.39519',
        city_only: 1,  // 只返回同城门店
        limit: 10
    },
    success: function(response) {
        if (response.code === 200) {
            const stores = response.data.stores;
            const currentCity = response.data.current_city;
            
            console.log('当前城市:', currentCity.city_name);
            console.log('附近门店:', stores);
            
            // 渲染门店列表
            stores.forEach(store => {
                console.log(`${store.name} - ${store.distance_text}`);
            });
        }
    }
});

// 2. 获取所有附近门店 (不限城市)
$.ajax({
    url: 'http://localhost:8000/api/stores/nearby',
    method: 'GET',
    data: {
        longitude: '115.10888',
        latitude: '33.39519',
        city_only: 0,  // 不限制城市
        radius: 100,   // 100公里范围
        limit: 20
    },
    success: function(response) {
        // 处理响应
    }
});
```

### Fetch API 调用

```javascript
// 使用 Fetch API
async function getNearbyStores(longitude, latitude) {
    try {
        const response = await fetch(
            `http://localhost:8000/api/stores/nearby?longitude=${longitude}&latitude=${latitude}&city_only=1`
        );
        
        const data = await response.json();
        
        if (data.code === 200) {
            return data.data;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('获取门店失败:', error);
        return null;
    }
}

// 使用示例
getNearbyStores('115.10888', '33.39519').then(result => {
    if (result) {
        console.log('当前城市:', result.current_city.city_name);
        console.log('门店列表:', result.stores);
    }
});
```

### cURL 调用

```bash
# 1. 搜索附近门店
curl -X GET "http://localhost:8000/api/stores/nearby?longitude=115.10888&latitude=33.39519&city_only=1&limit=10"

# 2. 获取门店列表
curl -X GET "http://localhost:8000/api/stores?page=1&limit=20"

# 3. 按城市搜索
curl -X GET "http://localhost:8000/api/stores/search?city=郑州"

# 4. 获取门店详情
curl -X GET "http://localhost:8000/api/stores/1"

# 5. 获取所有城市
curl -X GET "http://localhost:8000/api/cities"

# 6. 按首字母筛选城市
curl -X GET "http://localhost:8000/api/cities?first_char=B"

# 7. 搜索城市
curl -X GET "http://localhost:8000/api/cities?keyword=北京"

# 8. 获取分组城市
curl -X GET "http://localhost:8000/api/cities/grouped"

# 9. 获取城市详情
curl -X GET "http://localhost:8000/api/cities/110100"
```

### Python 调用

```python
import requests

def get_nearby_stores(longitude, latitude, city_only=True, limit=10):
    """获取附近门店"""
    url = "http://localhost:8000/api/stores/nearby"
    
    params = {
        'longitude': longitude,
        'latitude': latitude,
        'city_only': 1 if city_only else 0,
        'limit': limit
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data['code'] == 200:
            return data['data']
        else:
            print(f"API错误: {data['message']}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
result = get_nearby_stores('115.10888', '33.39519')
if result:
    print(f"当前城市: {result['current_city']['city_name']}")
    print(f"门店数量: {len(result['stores'])}")
    
    for store in result['stores']:
        print(f"- {store['name']} ({store['distance_text']})")

def get_all_cities():
    """获取所有城市列表"""
    try:
        response = requests.get("http://localhost:8000/api/cities", timeout=10)
        response.raise_for_status()

        data = response.json()
        if data['code'] == 200:
            return data['data']
        else:
            print(f"API错误: {data['message']}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def get_grouped_cities():
    """获取分组城市列表"""
    try:
        response = requests.get("http://localhost:8000/api/cities/grouped", timeout=10)
        response.raise_for_status()

        data = response.json()
        if data['code'] == 200:
            return data['data']
        else:
            print(f"API错误: {data['message']}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
cities = get_all_cities()
if cities:
    print(f"总共有 {len(cities)} 个城市")
    for city in cities[:5]:  # 显示前5个
        print(f"- {city['city_name']} ({city['city_code']})")

grouped_cities = get_grouped_cities()
if grouped_cities:
    print(f"按首字母分组:")
    for char, city_list in list(grouped_cities.items())[:3]:  # 显示前3组
        print(f"  {char}: {len(city_list)}个城市")
```

## 重要说明

### 1. 推荐使用 city_only=1

为了获得最佳的用户体验，强烈推荐使用 `city_only=1` 参数：

- ✅ **一致性**: 确保所有返回的门店都在同一城市
- ✅ **相关性**: 避免返回过远的门店
- ✅ **用户体验**: 符合用户就近选择的习惯

### 2. 坐标格式

- 经度范围: -180 到 180
- 纬度范围: -90 到 90
- 精度建议: 保留6位小数

### 3. 距离计算

使用球面距离公式计算，单位为公里，精确到小数点后1位。

### 4. 调试信息

响应中的 `debug_info` 字段包含详细的执行信息，生产环境可以移除。

## 错误处理

### 常见错误

```json
{
    "code": 400,
    "message": "经纬度参数不能为空",
    "data": null
}
```

### 错误处理建议

```javascript
function handleApiResponse(response) {
    switch(response.code) {
        case 200:
            return response.data;
        case 400:
            alert('请求参数错误: ' + response.message);
            break;
        case 404:
            alert('未找到相关门店');
            break;
        case 500:
            alert('服务器错误，请稍后重试');
            break;
        default:
            alert('未知错误: ' + response.message);
    }
    return null;
}
```

## 性能优化建议

1. **缓存结果**: 对于相同坐标的请求，可以缓存结果5-10分钟
2. **合理设置limit**: 根据UI需求设置合适的门店数量限制
3. **错误重试**: 网络错误时可以自动重试1-2次
4. **超时设置**: 建议设置10秒超时时间

## 更新日志

- **v4.0** (2024-12-19): 使用ThinkPHP查询构建器，优化城市筛选逻辑
- **v3.0** (2024-12-19): 修复路由顺序问题
- **v2.0** (2024-12-19): 添加智能城市筛选功能
- **v1.0** (2024-12-19): 初始版本
