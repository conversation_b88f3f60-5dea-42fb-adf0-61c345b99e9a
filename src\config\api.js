// API配置文件 - 基于真实数据库API文档
export const API_CONFIG = {
  // 菜单API配置 - 基于真实数据库
  MENU_API: {
    BASE_URL: 'http://localhost:8000',
    ENDPOINTS: {
      CATEGORIES: '/api/mobile/menu/categories',
      DISHES: '/api/mobile/menu/dishes',
      DISH_DETAIL: '/api/mobile/menu/dishes',
      RECOMMENDED: '/api/mobile/menu/recommended',
      SEARCH: '/api/mobile/menu/search'
    }
  },

  // 订单API配置
  ORDER_API: {
    BASE_URL: 'http://localhost:8000',
    ENDPOINTS: {
      ORDERS: '/api/mobile/orders',
      ORDER_DETAIL: '/api/mobile/orders',
      ORDER_STATUS: '/api/mobile/orders',
      CANCEL_ORDER: '/api/mobile/orders'
    }
  },

  // 其他API配置
  STORE_API: {
    BASE_URL: '/api',
    ENDPOINTS: {
      STORES: '/stores',
      STORE_DETAIL: '/stores'
    }
  },

  // 请求配置
  REQUEST_CONFIG: {
    TIMEOUT: 10000, // 10秒超时
    RETRY_COUNT: 3, // 重试次数
    RETRY_DELAY: 1000 // 重试延迟（毫秒）
  },

  // 真实数据库状态
  DATABASE_STATUS: {
    TOTAL_CATEGORIES: 9,
    TOTAL_DISHES: 9,
    AVAILABLE_DISHES: 0, // 所有餐品都售罄
    RECOMMENDED_DISHES: 0,
    VEGETARIAN_DISHES: 0
  }
}

// 环境配置 - 使用代理模式
export const ENV_CONFIG = {
  development: {
    MENU_API_BASE_URL: '', // 使用代理，不需要完整URL
    STORE_API_BASE_URL: '/api'
  },
  production: {
    MENU_API_BASE_URL: 'https://api.example.com',
    STORE_API_BASE_URL: '/api'
  }
}

// 获取当前环境配置
export const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  return ENV_CONFIG[env] || ENV_CONFIG.development
}
