import { defineStore } from 'pinia'
import apiService from '@/services/api'

export const useOrdersStore = defineStore('orders', {
  state: () => ({
    currentOrders: [], // 当前进行中的订单
    orderHistory: [], // 历史订单
    loading: false,
    error: null,
    customerPhone: '', // 当前用户手机号
    pollingIntervals: new Map() // 轮询定时器管理
  }),

  getters: {
    // 获取所有订单（当前+历史）
    allOrders: (state) => {
      return [...state.currentOrders, ...state.orderHistory]
    },

    // 获取进行中的订单数量
    activeOrdersCount: (state) => {
      return state.currentOrders.length
    },

    // 检查是否有已出餐的菜品
    hasReadyItems: (state) => {
      return state.currentOrders.some(order => 
        order.items.some(item => item.status === 'ready')
      )
    }
  },

  actions: {
    // 添加新订单
    addOrder(orderData) {
      const order = {
        orderNumber: orderData.orderNumber || 'ORD' + Date.now(),
        restaurant: orderData.restaurant,
        items: orderData.items.map(item => ({
          ...item,
          mealNumber: item.mealNumber || null,
          status: item.status || 'pending'
        })),
        totalAmount: orderData.items.length,
        orderTime: new Date().toISOString(),
        overallStatus: 'cooking'
      }

      this.currentOrders.unshift(order)
      this.saveToLocalStorage()
      return order
    },

    // 更新订单状态
    updateOrderStatus(orderNumber, updates) {
      const order = this.currentOrders.find(o => o.orderNumber === orderNumber)
      if (order) {
        Object.assign(order, updates)
        this.updateOverallStatus(order)
        this.saveToLocalStorage()
      }
    },

    // 更新订单项目
    updateOrderItems(orderNumber, items) {
      const order = this.currentOrders.find(o => o.orderNumber === orderNumber)
      if (order) {
        order.items = items
        this.updateOverallStatus(order)
        this.saveToLocalStorage()
      }
    },

    // 更新菜品状态
    updateItemStatus(orderNumber, itemId, status) {
      const order = this.currentOrders.find(o => o.orderNumber === orderNumber)
      if (order) {
        const item = order.items.find(i => i.id === itemId)
        if (item) {
          item.status = status
          this.updateOverallStatus(order)
          this.saveToLocalStorage()
        }
      }
    },

    // 更新订单整体状态
    updateOverallStatus(order) {
      const statuses = order.items.map(item => item.status)
      
      if (statuses.every(status => status === 'completed')) {
        order.overallStatus = 'completed'
        // 将完成的订单移到历史记录
        this.moveToHistory(order.orderNumber)
      } else if (statuses.some(status => status === 'ready')) {
        order.overallStatus = 'partial'
      } else if (statuses.every(status => status === 'cooking')) {
        order.overallStatus = 'cooking'
      }
    },

    // 将订单移到历史记录
    moveToHistory(orderNumber) {
      const orderIndex = this.currentOrders.findIndex(o => o.orderNumber === orderNumber)
      if (orderIndex > -1) {
        const order = this.currentOrders.splice(orderIndex, 1)[0]
        this.orderHistory.unshift(order)
        this.saveToLocalStorage()
      }
    },

    // 生成出餐号
    generateMealNumber() {
      return String(Math.floor(Math.random() * 900) + 100)
    },

    // 获取订单详情
    getOrder(orderNumber) {
      return this.allOrders.find(order => order.orderNumber === orderNumber)
    },

    // 清除错误
    clearError() {
      this.error = null
    },

    // 保存到本地存储
    saveToLocalStorage() {
      try {
        localStorage.setItem('diningOrders', JSON.stringify({
          currentOrders: this.currentOrders,
          orderHistory: this.orderHistory
        }))
      } catch (error) {
        console.error('保存订单到本地存储失败:', error)
      }
    },

    // 从本地存储加载
    loadFromLocalStorage() {
      try {
        const saved = localStorage.getItem('diningOrders')
        if (saved) {
          const data = JSON.parse(saved)
          this.currentOrders = data.currentOrders || []
          this.orderHistory = data.orderHistory || []
        }
      } catch (error) {
        console.error('从本地存储加载订单失败:', error)
        this.currentOrders = []
        this.orderHistory = []
      }
    },

    // 模拟订单状态更新（用于演示）
    simulateStatusUpdates() {
      this.currentOrders.forEach(order => {
        order.items.forEach(item => {
          if (item.status === 'cooking' && Math.random() < 0.1) {
            item.status = 'ready'
          } else if (item.status === 'ready' && Math.random() < 0.05) {
            item.status = 'completed'
          }
        })
        this.updateOverallStatus(order)
      })
      this.saveToLocalStorage()
    },

    // ==================== API相关方法 ====================

    // 设置用户手机号
    setCustomerPhone(phone) {
      this.customerPhone = phone
      localStorage.setItem('customerPhone', phone)
    },

    // 获取用户手机号
    getCustomerPhone() {
      if (!this.customerPhone) {
        this.customerPhone = localStorage.getItem('customerPhone') || ''
      }
      return this.customerPhone
    },

    // 创建订单（使用真实API）
    async createOrderAPI(orderData) {
      this.loading = true
      this.error = null

      try {
        // 构建API请求数据（包含店铺编号）
        const apiOrderData = {
          customer_name: '顾客', // API要求必填
          customer_phone: '13800000000', // API要求必填，使用默认值
          shop_code: orderData.shopCode || orderData.restaurant?.shop_code || 'DEFAULT_SHOP', // 店铺编号
          items: orderData.items.map(item => ({
            dish_id: item.menu_item_id || item.id,
            quantity: 1, // 每个item代表一份
            special_requirements: item.special_requirements || ''
          })),
          dining_type: orderData.diningType || 'dine_in',
          table_no: orderData.tableNumber || '',
          remark: orderData.specialRequirements || ''
        }

        console.log('🚀 创建订单API请求:', apiOrderData)
        console.log('🏪 店铺编号:', apiOrderData.shop_code)
        const result = await apiService.createOrder(apiOrderData)
        console.log('✅ 订单创建成功:', result)

        // 将API返回的订单添加到本地状态
        const localOrder = this.convertApiOrderToLocal(result)
        this.currentOrders.unshift(localOrder)
        this.saveToLocalStorage()

        // 开始轮询订单状态，如果失败则使用模拟
        try {
          this.startPollingOrder(result.id)
        } catch (error) {
          console.warn(`⚠️ 无法启动API轮询，使用状态模拟: ${error.message}`)
          this.startSimulatedPolling(result.id)
        }

        return localOrder
      } catch (error) {
        console.error('❌ 创建订单失败:', error)
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取订单详情（使用真实API）
    async fetchOrderDetail(orderId) {
      try {
        const result = await apiService.getOrderDetail(orderId)
        const localOrder = this.convertApiOrderToLocal(result)

        // 更新本地订单状态
        const existingOrderIndex = this.currentOrders.findIndex(o => o.apiOrderId === orderId)
        if (existingOrderIndex > -1) {
          this.currentOrders[existingOrderIndex] = localOrder
        } else {
          this.currentOrders.unshift(localOrder)
        }

        this.saveToLocalStorage()
        return localOrder
      } catch (error) {
        console.error('❌ 获取订单详情失败:', error)
        this.error = error.message
        throw error
      }
    },

    // 获取用户所有订单
    async fetchUserOrders() {
      const customerPhone = this.getCustomerPhone()
      if (!customerPhone) {
        console.log('ℹ️ 未设置用户手机号，仅显示本地订单')
        return
      }

      this.loading = true
      this.error = null

      try {
        const result = await apiService.getUserOrders(customerPhone, '', 1, 50)
        console.log('📋 获取用户订单:', result)

        if (result && result.list) {
          // 分离活跃订单和历史订单
          const activeOrders = []
          const historyOrders = []

          result.list.forEach(apiOrder => {
            const localOrder = this.convertApiOrderToLocal(apiOrder)

            if (['pending', 'confirmed', 'preparing', 'ready', 'serving'].includes(apiOrder.status)) {
              activeOrders.push(localOrder)
              // 为活跃订单启动轮询
              this.startPollingOrder(apiOrder.id)
            } else {
              historyOrders.push(localOrder)
            }
          })

          this.currentOrders = activeOrders
          this.orderHistory = historyOrders
          this.saveToLocalStorage()
        }
      } catch (error) {
        console.error('❌ 获取用户订单失败:', error)
        this.error = error.message
      } finally {
        this.loading = false
      }
    },

    // 取消订单
    async cancelOrderAPI(orderId, reason = '用户取消') {
      try {
        await apiService.cancelOrder(orderId, reason)

        // 更新本地订单状态
        const order = this.currentOrders.find(o => o.apiOrderId === orderId)
        if (order) {
          order.overallStatus = 'cancelled'
          this.moveToHistory(order.orderNumber)
        }

        // 停止轮询
        this.stopPollingOrder(orderId)

        console.log('✅ 订单取消成功')
      } catch (error) {
        console.error('❌ 取消订单失败:', error)
        this.error = error.message
        throw error
      }
    },

    // 开始轮询订单状态
    startPollingOrder(orderId) {
      // 避免重复轮询
      if (this.pollingIntervals.has(orderId)) {
        return
      }

      console.log(`🔄 开始轮询订单 ${orderId} 状态`)

      let errorCount = 0
      const maxErrors = 3

      const pollInterval = setInterval(async () => {
        try {
          const status = await apiService.getOrderStatus(orderId)
          console.log(`📊 订单 ${orderId} 状态:`, status)

          // 重置错误计数
          errorCount = 0

          // 更新本地订单状态
          const order = this.currentOrders.find(o => o.apiOrderId === orderId)
          if (order) {
            order.overallStatus = this.convertApiStatusToLocal(status.status)
            this.saveToLocalStorage()
          }

          // 如果订单已完成，停止轮询
          if (['completed', 'cancelled', 'refunded'].includes(status.status)) {
            console.log(`✅ 订单 ${orderId} 已完成，停止轮询`)
            this.stopPollingOrder(orderId)
          }
        } catch (error) {
          errorCount++
          console.error(`❌ 轮询订单 ${orderId} 状态失败 (${errorCount}/${maxErrors}):`, error.message)

          // 如果连续失败次数过多，停止轮询
          if (errorCount >= maxErrors) {
            console.error(`🛑 订单 ${orderId} 轮询失败次数过多，停止轮询`)
            this.stopPollingOrder(orderId)
          }
        }
      }, 30000) // 每30秒轮询一次

      this.pollingIntervals.set(orderId, pollInterval)
    },

    // 停止轮询订单状态
    stopPollingOrder(orderId) {
      const interval = this.pollingIntervals.get(orderId)
      if (interval) {
        clearInterval(interval)
        this.pollingIntervals.delete(orderId)
        console.log(`⏹️ 停止轮询订单 ${orderId} 状态`)
      }
    },

    // 停止所有轮询
    stopAllPolling() {
      this.pollingIntervals.forEach((interval, orderId) => {
        clearInterval(interval)
        console.log(`⏹️ 停止轮询订单 ${orderId} 状态`)
      })
      this.pollingIntervals.clear()
    },

    // 启动简化的状态模拟（当API不可用时）
    startSimulatedPolling(orderId) {
      if (this.pollingIntervals.has(`sim_${orderId}`)) {
        return
      }

      console.log(`🎭 启动订单 ${orderId} 状态模拟`)

      const simulateInterval = setInterval(() => {
        const order = this.currentOrders.find(o => o.apiOrderId === orderId)
        if (!order) {
          this.stopSimulatedPolling(orderId)
          return
        }

        // 模拟状态进展
        const statusProgression = ['pending', 'cooking', 'ready', 'completed']
        const currentIndex = statusProgression.indexOf(order.overallStatus)

        if (currentIndex < statusProgression.length - 1) {
          order.overallStatus = statusProgression[currentIndex + 1]
          console.log(`🎭 模拟订单 ${orderId} 状态更新为: ${order.overallStatus}`)
          this.saveToLocalStorage()

          // 如果到达完成状态，停止模拟
          if (order.overallStatus === 'completed') {
            this.stopSimulatedPolling(orderId)
          }
        }
      }, 60000) // 每分钟更新一次状态

      this.pollingIntervals.set(`sim_${orderId}`, simulateInterval)
    },

    // 停止状态模拟
    stopSimulatedPolling(orderId) {
      const interval = this.pollingIntervals.get(`sim_${orderId}`)
      if (interval) {
        clearInterval(interval)
        this.pollingIntervals.delete(`sim_${orderId}`)
        console.log(`⏹️ 停止订单 ${orderId} 状态模拟`)
      }
    },

    // 将API订单数据转换为本地格式
    convertApiOrderToLocal(apiOrder) {
      return {
        orderNumber: `ORD${apiOrder.id}`,
        apiOrderId: apiOrder.id,
        restaurant: {
          id: apiOrder.store_id,
          name: apiOrder.store_name || '餐厅'
        },
        items: apiOrder.items?.map(item => ({
          id: item.id,
          menu_item_id: item.dish_id,
          name: item.dish_name || item.product_name,
          quantity: item.quantity,
          phone: item.phone || this.generatePhoneForDish(item.dish_id),
          mealNumber: item.meal_number,
          status: this.convertApiStatusToLocal(item.status),
          special_requirements: item.special_requirements
        })) || [],
        totalAmount: apiOrder.total_amount,
        orderTime: apiOrder.order_time || apiOrder.created_at || new Date().toISOString(),
        overallStatus: this.convertApiStatusToLocal(apiOrder.status),
        customerPhone: apiOrder.customer_phone,
        customerName: apiOrder.customer_name,
        paymentMethod: apiOrder.payment_method,
        diningType: apiOrder.dining_type,
        tableNumber: apiOrder.table_no || apiOrder.table_number,
        specialRequirements: apiOrder.remark || apiOrder.special_requirements,
        // 新增字段
        orderNo: apiOrder.order_no,
        paymentStatus: apiOrder.payment_status,
        paymentStatusText: apiOrder.payment_status_text,
        progress: apiOrder.progress || 0
      }
    },

    // 转换API状态到本地状态
    convertApiStatusToLocal(apiStatus) {
      const statusMap = {
        'pending': 'pending',
        'confirmed': 'cooking',
        'preparing': 'cooking',
        'ready': 'ready',
        'serving': 'ready',
        'completed': 'completed',
        'cancelled': 'cancelled',
        'refunded': 'cancelled'
      }
      return statusMap[apiStatus] || 'pending'
    },

    // 为菜品生成手机号
    generatePhoneForDish(dishId) {
      const phones = [
        '13812345678', '13987654321', '15612345678', '18612345678',
        '13712345678', '15987654321', '18987654321', '13612345678'
      ]
      return phones[(dishId - 1) % phones.length]
    },

    // 初始化
    init() {
      this.loadFromLocalStorage()

      // 如果有用户手机号，获取订单列表
      const customerPhone = this.getCustomerPhone()
      if (customerPhone) {
        console.log('📱 检测到用户手机号，获取订单列表:', customerPhone)
        this.fetchUserOrders()
      } else {
        console.log('ℹ️ 未设置用户手机号，使用本地订单数据')
      }

      // 启动状态更新模拟（仅用于演示，如果没有API）
      setInterval(() => {
        if (this.currentOrders.length > 0 && !customerPhone) {
          this.simulateStatusUpdates()
        }
      }, 10000) // 每10秒更新一次
    }
  }
})
