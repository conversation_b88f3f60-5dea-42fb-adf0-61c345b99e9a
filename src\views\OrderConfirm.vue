<template>
  <div class="order-confirm">
    <!-- 顶部导航 -->
    <div class="header">
      <button class="back-btn" @click="goBack">←</button>
      <h1>确认订单</h1>
    </div>

    <!-- 餐厅信息 -->
    <div class="restaurant-info" v-if="restaurant">
      <h3>{{ restaurant.name }}</h3>
      <p>{{ restaurant.address }}</p>
    </div>

    <!-- 订单详情 -->
    <div class="order-details">
      <h3>订单详情</h3>
      <div class="order-items">
        <div v-for="item in orderItems" :key="item.id" class="order-item">
          <span class="item-name">{{ item.name }}</span>
        </div>
      </div>
      <div class="order-total">
        <span>共 {{ orderItems.length }} 道菜品</span>
      </div>
    </div>



    <!-- 确认按钮 -->
    <div class="confirm-section">
      <button
        class="confirm-btn"
        @click="submitOrder"
        :disabled="submitting"
      >
        {{ submitting ? '提交中...' : '确认点餐' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'

export default {
  name: 'OrderConfirm',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const ordersStore = useOrdersStore()

    const restaurantId = route.params.restaurantId
    const restaurant = ref({})
    const orderItems = ref([])
    const submitting = ref(false)

    const totalAmount = computed(() => {
      return orderItems.value.length
    })

    const goBack = () => {
      router.go(-1)
    }

    const submitOrder = async () => {
      submitting.value = true

      try {
        // 创建订单数据（不包含用户信息）
        const orderData = {
          restaurant: restaurant.value,
          items: orderItems.value,
          totalAmount: totalAmount.value,
          paymentMethod: 'cash', // 默认现金支付
          diningType: 'dine_in', // 默认堂食
          tableNumber: '', // 可以后续添加桌号
          specialRequirements: '' // 可以后续添加特殊要求
        }

        console.log('📝 提交订单数据:', orderData)

        // 使用真实API创建订单
        const order = await ordersStore.createOrderAPI(orderData)

        console.log('✅ 订单创建成功:', order)

        // 跳转到点餐成功页面
        router.replace({
          name: 'OrderSuccess',
          params: { orderNumber: order.orderNumber }
        })

      } catch (error) {
        console.error('❌ 提交订单失败:', error)
        alert(`提交订单失败: ${error.message}`)
      } finally {
        submitting.value = false
      }
    }

    onMounted(() => {
      // 获取购物车数据
      try {
        const cartData = route.query.cartData
        if (cartData) {
          orderItems.value = JSON.parse(cartData)
        } else {
          // 降级到历史状态
          const cartItems = history.state?.cartItems || []
          orderItems.value = cartItems
        }
      } catch (error) {
        console.error('解析购物车数据失败:', error)
        orderItems.value = []
      }

      // 获取餐厅数据（从菜单页面传递或使用默认值）
      const restaurantData = route.query.restaurant
      if (restaurantData) {
        try {
          restaurant.value = JSON.parse(restaurantData)
        } catch (error) {
          console.error('解析餐厅数据失败:', error)
          restaurant.value = {
            id: restaurantId,
            name: '美味餐厅',
            address: '北京市朝阳区某某街道123号'
          }
        }
      } else {
        restaurant.value = {
          id: restaurantId,
          name: '美味餐厅',
          address: '北京市朝阳区某某街道123号'
        }
      }


    })

    return {
      restaurant,
      orderItems,
      submitting,
      totalAmount,
      goBack,
      submitOrder
    }
  }
}
</script>

<style scoped>
.order-confirm {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  margin-right: 12px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.restaurant-info {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.restaurant-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.restaurant-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.order-details {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.order-details h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 14px;
}

.order-total {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px solid #f0f0f0;
  text-align: right;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}





.confirm-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #eee;
}

.confirm-btn {
  width: 100%;
  background: #007AFF;
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.confirm-btn:hover:not(:disabled) {
  background: #0056b3;
}

.confirm-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
