# 📱 手机端订单API文档

## 概述

本文档描述了专为手机前端优化的订单API接口，包括创建订单、查询订单状态、订单管理等功能。

## 基础信息

- **基础URL**: `http://localhost:8000/api/mobile/orders`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证**: 无需认证

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据内容
    }
}
```

## API接口列表

### 1. 创建订单

**接口地址**: `POST /api/mobile/orders`

**功能描述**: 创建新订单，支持多个餐品，自动计算金额和库存管理

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customer_name | string | 是 | 客户姓名 |
| customer_phone | string | 是 | 客户手机号 (11位) |
| customer_email | string | 否 | 客户邮箱 |
| customer_address | string | 否 | 客户地址 |
| table_no | string | 否 | 桌号 |
| dining_type | string | 否 | 用餐类型 (dine_in/takeout/delivery) |
| remark | string | 否 | 订单备注 |
| kitchen_memo | string | 否 | 厨房备注 |
| service_fee | float | 否 | 服务费 |
| delivery_fee | float | 否 | 配送费 |
| discount_amount | float | 否 | 优惠金额 |
| items | array | 是 | 订单明细 |

**订单明细 (items) 结构**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dish_id | int | 是 | 餐品ID |
| quantity | int | 是 | 数量 |
| special_requirements | string | 否 | 特殊要求 |

**请求示例**:

```json
{
    "customer_name": "张三",
    "customer_phone": "13800138000",
    "customer_address": "北京市朝阳区xxx",
    "table_no": "A01",
    "dining_type": "dine_in",
    "remark": "少盐少油",
    "service_fee": 2.0,
    "items": [
        {
            "dish_id": 1,
            "quantity": 2,
            "special_requirements": "不要辣椒"
        },
        {
            "dish_id": 2,
            "quantity": 1,
            "special_requirements": ""
        }
    ]
}
```

**响应示例**:

```json
{
    "code": 200,
    "message": "订单创建成功",
    "data": {
        "id": 1,
        "order_no": "M20241219143025001",
        "customer_name": "张三",
        "customer_phone": "13800138000",
        "customer_address": "北京市朝阳区xxx",
        "table_no": "A01",
        "dining_type": "dine_in",
        "dining_type_text": "堂食",
        "status": "confirmed",
        "status_text": "已确认",
        "subtotal_amount": 46.00,
        "service_fee": 2.00,
        "delivery_fee": 0.00,
        "discount_amount": 0.00,
        "total_amount": 48.00,
        "progress": 20,
        "order_time": "2024-12-19 14:30:25",
        "items": [
            {
                "id": 1,
                "product_id": 1,
                "product_name": "宫保鸡丁",
                "unit_price": 28.00,
                "quantity": 2,
                "total_price": 56.00,
                "special_requirements": "不要辣椒",
                "status": "pending",
                "status_text": "待确认",
                "kitchen_status": "waiting",
                "kitchen_status_text": "等待制作",
                "dish_image": "http://localhost:8000/uploads/dishes/gongbao.jpg"
            }
        ],
        "item_count": 2,
        "total_quantity": 3
    }
}
```

### 2. 获取订单详情

**接口地址**: `GET /api/mobile/orders/{id}`

**功能描述**: 根据订单ID获取订单详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "order_no": "M20241219143025001",
        "customer_name": "张三",
        "customer_phone": "13800138000",
        "status": "preparing",
        "status_text": "制作中",
        "payment_status": "paid",
        "payment_status_text": "已支付",
        "progress": 50,
        "total_amount": 48.00,
        "paid_amount": 48.00,
        "order_time": "2024-12-19 14:30:25",
        "items": [
            {
                "product_name": "宫保鸡丁",
                "quantity": 2,
                "unit_price": 28.00,
                "total_price": 56.00,
                "status_text": "制作中",
                "kitchen_status_text": "制作中",
                "dish_image": "http://localhost:8000/uploads/dishes/gongbao.jpg"
            }
        ],
        "latest_status_change": {
            "status": "preparing",
            "status_text": "制作中",
            "change_time": "2024-12-19 14:35:00",
            "operator": "厨师"
        }
    }
}
```

### 3. 获取用户订单列表

**接口地址**: `GET /api/mobile/orders`

**功能描述**: 根据手机号获取用户的订单列表

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| customer_phone | string | 是 | - | 客户手机号 |
| status | string | 否 | - | 订单状态筛选 |
| page | int | 否 | 1 | 页码 |
| limit | int | 否 | 10 | 每页数量 |

**订单状态值**:
- `pending` - 待确认
- `confirmed` - 已确认
- `preparing` - 制作中
- `ready` - 待上菜
- `serving` - 上菜中
- `completed` - 已完成
- `cancelled` - 已取消

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "order_no": "M20241219143025001",
                "status": "preparing",
                "status_text": "制作中",
                "payment_status": "paid",
                "payment_status_text": "已支付",
                "progress": 50,
                "total_amount": 48.00,
                "paid_amount": 48.00,
                "order_time": "2024-12-19 14:30:25",
                "item_types": 2,
                "total_quantity": 3,
                "thumbnail": "http://localhost:8000/uploads/dishes/gongbao.jpg"
            }
        ],
        "pagination": {
            "total": 15,
            "page": 1,
            "limit": 10,
            "pages": 2
        }
    }
}
```

### 4. 取消订单

**接口地址**: `PUT /api/mobile/orders/{id}/cancel`

**功能描述**: 取消订单并恢复库存

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| cancel_reason | string | 否 | 用户取消 | 取消原因 |

**请求示例**:

```json
{
    "cancel_reason": "临时有事，无法用餐"
}
```

**响应示例**:

```json
{
    "code": 200,
    "message": "订单取消成功",
    "data": []
}
```

**注意事项**:
- 只有状态为 `pending` 或 `confirmed` 的订单可以取消
- 取消订单会自动恢复餐品库存
- 已支付的订单取消后需要手动处理退款

### 5. 获取订单状态

**接口地址**: `GET /api/mobile/orders/{id}/status`

**功能描述**: 获取订单当前状态，适用于轮询更新

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |

**响应示例**:

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "order_no": "M20241219143025001",
        "status": "preparing",
        "status_text": "制作中",
        "payment_status": "paid",
        "payment_status_text": "已支付",
        "progress": 50,
        "total_amount": 48.00,
        "paid_amount": 48.00
    }
}
```

## 数据字典

### 用餐类型 (dining_type)

| 值 | 说明 |
|----|------|
| dine_in | 堂食 |
| takeout | 打包 |
| delivery | 外卖 |

### 支付方式 (payment_method)

| 值 | 说明 |
|----|------|
| wechat | 微信支付 |
| alipay | 支付宝 |
| cash | 现金 |
| card | 银行卡 |

### 订单状态 (status)

| 值 | 说明 | 进度 |
|----|------|------|
| pending | 待确认 | 10% |
| confirmed | 已确认 | 25% |
| preparing | 制作中 | 50% |
| ready | 待上菜 | 75% |
| serving | 上菜中 | 90% |
| completed | 已完成 | 100% |
| cancelled | 已取消 | 0% |
| refunded | 已退款 | 0% |

### 支付状态 (payment_status)

| 值 | 说明 |
|----|------|
| unpaid | 未支付 |
| partial | 部分支付 |
| paid | 已支付 |
| refunded | 已退款 |

## 错误处理

### 错误响应格式

```json
{
    "code": 400,
    "message": "错误描述",
    "data": []
}
```

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查必填参数和格式 |
| 404 | 订单不存在 | 确认订单ID是否正确 |
| 409 | 库存不足 | 减少数量或选择其他餐品 |
| 422 | 订单状态不允许操作 | 检查订单当前状态 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误示例

```json
{
    "code": 409,
    "message": "餐品 宫保鸡丁 库存不足",
    "data": []
}
```

## 前端集成示例

### JavaScript/Fetch API

```javascript
// 订单API封装类
class OrderAPI {
    constructor(baseURL = 'http://localhost:8000/api/mobile/orders') {
        this.baseURL = baseURL;
    }

    // 创建订单
    async createOrder(orderData) {
        const response = await fetch(this.baseURL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });
        return await this.handleResponse(response);
    }

    // 获取订单详情
    async getOrderDetail(orderId) {
        const response = await fetch(`${this.baseURL}/${orderId}`);
        return await this.handleResponse(response);
    }

    // 获取用户订单列表
    async getUserOrders(phone, status = '', page = 1) {
        const params = new URLSearchParams({ 
            customer_phone: phone, 
            page: page.toString() 
        });
        if (status) params.append('status', status);
        
        const response = await fetch(`${this.baseURL}?${params}`);
        return await this.handleResponse(response);
    }

    // 取消订单
    async cancelOrder(orderId, reason = '用户取消') {
        const response = await fetch(`${this.baseURL}/${orderId}/cancel`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cancel_reason: reason })
        });
        return await this.handleResponse(response);
    }

    // 获取订单状态
    async getOrderStatus(orderId) {
        const response = await fetch(`${this.baseURL}/${orderId}/status`);
        return await this.handleResponse(response);
    }

    // 统一响应处理
    async handleResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.code !== 200) {
            throw new Error(data.message);
        }
        
        return data.data;
    }
}

// 使用示例
const orderAPI = new OrderAPI();

// 创建订单
const orderData = {
    customer_name: '张三',
    customer_phone: '13800138000',
    dining_type: 'dine_in',
    items: [
        { dish_id: 1, quantity: 2, special_requirements: '不要辣椒' },
        { dish_id: 2, quantity: 1 }
    ]
};

try {
    const order = await orderAPI.createOrder(orderData);
    console.log('订单创建成功:', order);
    
    // 轮询订单状态
    const pollStatus = setInterval(async () => {
        try {
            const status = await orderAPI.getOrderStatus(order.id);
            console.log('订单状态:', status);
            
            if (status.status === 'completed' || status.status === 'cancelled') {
                clearInterval(pollStatus);
            }
        } catch (error) {
            console.error('获取状态失败:', error);
        }
    }, 30000); // 每30秒轮询一次
    
} catch (error) {
    console.error('创建订单失败:', error);
}
```

### React Hook 示例

```javascript
import { useState, useEffect } from 'react';

// 自定义Hook：订单管理
function useOrders(customerPhone) {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const orderAPI = new OrderAPI();

    // 加载订单列表
    const loadOrders = async (status = '') => {
        try {
            setLoading(true);
            const data = await orderAPI.getUserOrders(customerPhone, status);
            setOrders(data.list);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    // 创建订单
    const createOrder = async (orderData) => {
        try {
            setLoading(true);
            const order = await orderAPI.createOrder(orderData);
            await loadOrders(); // 重新加载列表
            return order;
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    };

    // 取消订单
    const cancelOrder = async (orderId, reason) => {
        try {
            await orderAPI.cancelOrder(orderId, reason);
            await loadOrders(); // 重新加载列表
        } catch (err) {
            setError(err.message);
            throw err;
        }
    };

    useEffect(() => {
        if (customerPhone) {
            loadOrders();
        }
    }, [customerPhone]);

    return {
        orders,
        loading,
        error,
        loadOrders,
        createOrder,
        cancelOrder
    };
}

// 订单组件
function OrderComponent({ customerPhone }) {
    const { orders, loading, error, createOrder, cancelOrder } = useOrders(customerPhone);

    const handleCreateOrder = async () => {
        const orderData = {
            customer_name: '张三',
            customer_phone: customerPhone,
            dining_type: 'dine_in',
            items: [
                { dish_id: 1, quantity: 2 }
            ]
        };

        try {
            const order = await createOrder(orderData);
            alert(`订单创建成功: ${order.order_no}`);
        } catch (error) {
            alert(`创建失败: ${error.message}`);
        }
    };

    const handleCancelOrder = async (orderId) => {
        if (confirm('确定要取消这个订单吗？')) {
            try {
                await cancelOrder(orderId, '用户主动取消');
                alert('订单取消成功');
            } catch (error) {
                alert(`取消失败: ${error.message}`);
            }
        }
    };

    if (loading) return <div>加载中...</div>;
    if (error) return <div>错误: {error}</div>;

    return (
        <div className="orders">
            <button onClick={handleCreateOrder}>创建测试订单</button>
            
            <div className="order-list">
                {orders.map(order => (
                    <div key={order.id} className="order-item">
                        <h3>订单号: {order.order_no}</h3>
                        <p>状态: {order.status_text}</p>
                        <p>金额: ¥{order.total_amount}</p>
                        <p>进度: {order.progress}%</p>
                        
                        {order.status === 'pending' && (
                            <button onClick={() => handleCancelOrder(order.id)}>
                                取消订单
                            </button>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
}
```

## 最佳实践

### 1. 订单状态轮询

```javascript
// 推荐的轮询策略
function pollOrderStatus(orderId, callback) {
    const poll = async () => {
        try {
            const status = await orderAPI.getOrderStatus(orderId);
            callback(status);
            
            // 根据状态决定是否继续轮询
            if (!['completed', 'cancelled', 'refunded'].includes(status.status)) {
                setTimeout(poll, 30000); // 30秒后再次轮询
            }
        } catch (error) {
            console.error('轮询失败:', error);
            setTimeout(poll, 60000); // 出错时60秒后重试
        }
    };
    
    poll();
}
```

### 2. 错误处理

```javascript
// 统一错误处理
function handleOrderError(error) {
    if (error.message.includes('库存不足')) {
        // 显示库存不足提示
        showStockAlert();
    } else if (error.message.includes('订单不存在')) {
        // 跳转到订单列表
        redirectToOrderList();
    } else {
        // 显示通用错误提示
        showErrorAlert(error.message);
    }
}
```

### 3. 性能优化

- **缓存订单列表**: 避免频繁请求
- **分页加载**: 大量订单时使用分页
- **状态轮询**: 合理设置轮询间隔
- **错误重试**: 网络错误时自动重试

## 测试工具

### 在线测试
访问测试页面：[http://localhost:8000/api_examples.html](http://localhost:8000/api_examples.html)

### 手动测试命令

```bash
# 创建订单
curl -X POST "http://localhost:8000/api/mobile/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_name": "张三",
    "customer_phone": "13800138000",
    "items": [{"dish_id": 1, "quantity": 2}]
  }'

# 获取订单详情
curl "http://localhost:8000/api/mobile/orders/1"

# 获取用户订单列表
curl "http://localhost:8000/api/mobile/orders?customer_phone=13800138000"

# 取消订单
curl -X PUT "http://localhost:8000/api/mobile/orders/1/cancel" \
  -H "Content-Type: application/json" \
  -d '{"cancel_reason": "测试取消"}'

# 获取订单状态
curl "http://localhost:8000/api/mobile/orders/1/status"
```

---

**版本**: v1.0  
**更新时间**: 2024-12-19  
**维护团队**: 开发团队
