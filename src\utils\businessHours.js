/**
 * 营业时间相关工具函数
 */

/**
 * 判断当前是否在营业时间内
 * @param {string} startTime - 开始营业时间，格式：'09:00'
 * @param {string} endTime - 结束营业时间，格式：'22:00'
 * @param {Date} currentTime - 当前时间，默认为现在
 * @returns {boolean} 是否营业中
 */
export function isBusinessOpen(startTime, endTime, currentTime = new Date()) {
  if (!startTime || !endTime) {
    return true // 如果没有营业时间信息，默认营业
  }

  try {
    const now = currentTime
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentTotalMinutes = currentHour * 60 + currentMinute

    // 解析开始时间
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const startTotalMinutes = startHour * 60 + startMinute

    // 解析结束时间
    const [endHour, endMinute] = endTime.split(':').map(Number)
    let endTotalMinutes = endHour * 60 + endMinute

    // 处理跨天营业的情况（如：22:00 - 02:00）
    if (endTotalMinutes <= startTotalMinutes) {
      // 跨天营业
      if (currentTotalMinutes >= startTotalMinutes || currentTotalMinutes <= endTotalMinutes) {
        return true
      }
    } else {
      // 当天营业
      if (currentTotalMinutes >= startTotalMinutes && currentTotalMinutes <= endTotalMinutes) {
        return true
      }
    }

    return false
  } catch (error) {
    console.error('解析营业时间失败:', error)
    return true // 解析失败时默认营业
  }
}

/**
 * 获取营业状态文本
 * @param {string} startTime - 开始营业时间
 * @param {string} endTime - 结束营业时间
 * @param {Date} currentTime - 当前时间
 * @returns {object} 包含状态和文本的对象
 */
export function getBusinessStatus(startTime, endTime, currentTime = new Date()) {
  const isOpen = isBusinessOpen(startTime, endTime, currentTime)
  
  return {
    isOpen,
    status: isOpen ? 'open' : 'closed',
    text: isOpen ? '营业中' : '已打烊',
    className: isOpen ? 'open' : 'closed'
  }
}

/**
 * 格式化营业时间显示
 * @param {string} startTime - 开始营业时间
 * @param {string} endTime - 结束营业时间
 * @returns {string} 格式化的营业时间文本
 */
export function formatBusinessHours(startTime, endTime) {
  if (!startTime || !endTime) {
    return '营业时间未知'
  }
  
  return `${startTime} - ${endTime}`
}

/**
 * 计算距离下次营业状态变化的时间
 * @param {string} startTime - 开始营业时间
 * @param {string} endTime - 结束营业时间
 * @param {Date} currentTime - 当前时间
 * @returns {object} 包含时间信息的对象
 */
export function getNextStatusChange(startTime, endTime, currentTime = new Date()) {
  if (!startTime || !endTime) {
    return null
  }

  try {
    const now = currentTime
    const isOpen = isBusinessOpen(startTime, endTime, currentTime)
    
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const [endHour, endMinute] = endTime.split(':').map(Number)
    
    let targetTime = new Date(now)
    
    if (isOpen) {
      // 当前营业中，计算到打烊的时间
      targetTime.setHours(endHour, endMinute, 0, 0)
      
      // 如果结束时间是第二天（跨天营业）
      if (endHour < startHour || (endHour === startHour && endMinute <= startMinute)) {
        if (now.getHours() < endHour || (now.getHours() === endHour && now.getMinutes() < endMinute)) {
          // 当前是第二天，不需要加一天
        } else {
          // 当前是第一天，需要加一天
          targetTime.setDate(targetTime.getDate() + 1)
        }
      }
      
      return {
        isOpen: true,
        nextStatus: 'closed',
        nextStatusText: '打烊',
        targetTime,
        message: `距离打烊还有`
      }
    } else {
      // 当前打烊中，计算到开业的时间
      targetTime.setHours(startHour, startMinute, 0, 0)
      
      // 如果开始时间已经过了，设置为明天
      if (targetTime <= now) {
        targetTime.setDate(targetTime.getDate() + 1)
      }
      
      return {
        isOpen: false,
        nextStatus: 'open',
        nextStatusText: '开业',
        targetTime,
        message: `距离开业还有`
      }
    }
  } catch (error) {
    console.error('计算下次状态变化失败:', error)
    return null
  }
}

/**
 * 格式化时间差
 * @param {Date} targetTime - 目标时间
 * @param {Date} currentTime - 当前时间
 * @returns {string} 格式化的时间差文本
 */
export function formatTimeDifference(targetTime, currentTime = new Date()) {
  const diff = targetTime.getTime() - currentTime.getTime()
  
  if (diff <= 0) {
    return '0分钟'
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 创建一个响应式的营业状态对象
 * @param {string} startTime - 开始营业时间
 * @param {string} endTime - 结束营业时间
 * @returns {object} 包含营业状态和更新方法的对象
 */
export function createBusinessStatusWatcher(startTime, endTime) {
  let status = getBusinessStatus(startTime, endTime)
  let updateCallback = null
  
  // 每分钟检查一次状态变化
  const interval = setInterval(() => {
    const newStatus = getBusinessStatus(startTime, endTime)
    if (newStatus.isOpen !== status.isOpen) {
      status = newStatus
      if (updateCallback) {
        updateCallback(status)
      }
    }
  }, 60000) // 每分钟检查一次
  
  return {
    getCurrentStatus: () => status,
    onStatusChange: (callback) => {
      updateCallback = callback
    },
    destroy: () => {
      clearInterval(interval)
    }
  }
}
