# 附近门店API使用指南

## 🚀 快速开始

### 最常用的API - 智能附近门店搜索

```
GET http://localhost:8000/api/stores/nearby?longitude=115.10888&latitude=33.39519&city_only=1
```

## 📋 核心参数

| 参数 | 必填 | 默认值 | 说明 |
|------|------|--------|------|
| `longitude` | ✅ | - | 经度 |
| `latitude` | ✅ | - | 纬度 |
| `city_only` | ❌ | 1 | 1=只返回同城门店, 0=返回所有附近门店 |
| `limit` | ❌ | 0 | 返回门店数量 (0=不限制，返回所有门店) |
| `radius` | ❌ | 50 | 搜索半径(公里) |

## 🎯 推荐用法

### 1. 智能同城搜索 (推荐) ⭐
```javascript
// 只返回最近门店所在城市的门店，确保结果一致性
fetch('http://localhost:8000/api/stores/nearby?longitude=115.10888&latitude=33.39519&city_only=1')
```

**优势**：
- ✅ 结果一致性好，所有门店都在同一城市
- ✅ 符合用户就近选择的习惯
- ✅ 避免跨城市的混乱结果

### 2. 全范围搜索
```javascript
// 返回指定范围内的所有门店，可能跨城市
fetch('http://localhost:8000/api/stores/nearby?longitude=115.10888&latitude=33.39519&city_only=0&radius=100')
```

## 📱 前端集成示例

### jQuery
```javascript
function searchNearbyStores(longitude, latitude) {
    $.get('http://localhost:8000/api/stores/nearby', {
        longitude: longitude,
        latitude: latitude,
        city_only: 1
        // limit: 0 (默认返回所有门店，可以不传)
    }).done(function(response) {
        if (response.code === 200) {
            const data = response.data;
            const stores = data.stores;
            const city = data.current_city.city_name;
            
            console.log(`${city}的门店:`, stores);
            
            // 渲染门店列表
            $('#store-list').empty();
            stores.forEach(function(store) {
                $('#store-list').append(`
                    <div class="store-item">
                        <h3>${store.name}</h3>
                        <p>📍 ${store.address}</p>
                        <p>📞 ${store.phone}</p>
                        <p>📏 ${store.distance_text}</p>
                    </div>
                `);
            });
        } else {
            alert('搜索失败: ' + response.message);
        }
    }).fail(function() {
        alert('网络错误，请重试');
    });
}

// 使用示例
searchNearbyStores('115.10888', '33.39519');
```

### Fetch API (现代浏览器)
```javascript
async function getNearbyStores(longitude, latitude) {
    try {
        const response = await fetch(
            `http://localhost:8000/api/stores/nearby?longitude=${longitude}&latitude=${latitude}&city_only=1`
        );
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.code === 200) {
            return data.data;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('获取门店失败:', error);
        throw error;
    }
}

// 使用示例
getNearbyStores('115.10888', '33.39519')
    .then(result => {
        console.log('当前城市:', result.current_city.city_name);
        console.log('最近门店:', result.nearest_store.name);
        console.log('门店列表:', result.stores);
        
        // 渲染到页面
        renderStores(result.stores);
    })
    .catch(error => {
        alert('搜索失败: ' + error.message);
    });

function renderStores(stores) {
    const container = document.getElementById('store-list');
    container.innerHTML = '';
    
    stores.forEach(store => {
        const storeElement = document.createElement('div');
        storeElement.className = 'store-item';
        storeElement.innerHTML = `
            <h3>${store.name}</h3>
            <p>📍 ${store.address}</p>
            <p>📞 ${store.phone || '暂无电话'}</p>
            <p>📏 距离: <strong>${store.distance_text}</strong></p>
            <p>🕐 营业时间: ${store.start_time} - ${store.end_time}</p>
        `;
        container.appendChild(storeElement);
    });
}
```

### Vue.js 组件
```vue
<template>
  <div class="store-search">
    <div class="search-form">
      <input v-model="longitude" placeholder="经度" />
      <input v-model="latitude" placeholder="纬度" />
      <button @click="searchStores" :disabled="loading">
        {{ loading ? '搜索中...' : '搜索门店' }}
      </button>
      <button @click="getCurrentLocation">获取当前位置</button>
    </div>
    
    <div v-if="currentCity" class="city-info">
      <h2>📍 {{ currentCity.city_name }}</h2>
      <p v-if="nearestStore">
        最近门店: {{ nearestStore.name }} ({{ nearestStore.distance_text }})
      </p>
    </div>
    
    <div class="store-list">
      <div v-for="store in stores" :key="store.id" class="store-item">
        <h3>{{ store.name }}</h3>
        <p>📍 {{ store.address }}</p>
        <p>📞 {{ store.phone }}</p>
        <p>📏 {{ store.distance_text }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StoreSearch',
  data() {
    return {
      longitude: '115.10888',
      latitude: '33.39519',
      stores: [],
      currentCity: null,
      nearestStore: null,
      loading: false
    }
  },
  methods: {
    async searchStores() {
      if (!this.longitude || !this.latitude) {
        alert('请输入经纬度');
        return;
      }
      
      this.loading = true;
      try {
        const response = await this.$http.get('/api/stores/nearby', {
          params: {
            longitude: this.longitude,
            latitude: this.latitude,
            city_only: 1,
            limit: 20
          }
        });
        
        if (response.data.code === 200) {
          const data = response.data.data;
          this.stores = data.stores;
          this.currentCity = data.current_city;
          this.nearestStore = data.nearest_store;
        } else {
          alert('搜索失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        alert('网络错误，请重试');
      } finally {
        this.loading = false;
      }
    },
    
    getCurrentLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          position => {
            this.longitude = position.coords.longitude.toFixed(6);
            this.latitude = position.coords.latitude.toFixed(6);
            this.searchStores();
          },
          error => {
            alert('无法获取位置: ' + error.message);
          }
        );
      } else {
        alert('浏览器不支持地理定位');
      }
    }
  },
  
  mounted() {
    // 组件加载时自动搜索
    this.searchStores();
  }
}
</script>

<style scoped>
.store-search {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.search-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-form input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-form button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-form button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.city-info {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.store-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.store-item h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.store-item p {
  margin: 5px 0;
  color: #666;
}
</style>
```

### React Hook
```javascript
import { useState, useEffect, useCallback } from 'react';

function useNearbyStores(longitude, latitude) {
  const [stores, setStores] = useState([]);
  const [currentCity, setCurrentCity] = useState(null);
  const [nearestStore, setNearestStore] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchStores = useCallback(async () => {
    if (!longitude || !latitude) return;

    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `http://localhost:8000/api/stores/nearby?longitude=${longitude}&latitude=${latitude}&city_only=1`
      );
      
      const data = await response.json();
      
      if (data.code === 200) {
        setStores(data.data.stores);
        setCurrentCity(data.data.current_city);
        setNearestStore(data.data.nearest_store);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('网络错误: ' + err.message);
    } finally {
      setLoading(false);
    }
  }, [longitude, latitude]);

  useEffect(() => {
    searchStores();
  }, [searchStores]);

  return {
    stores,
    currentCity,
    nearestStore,
    loading,
    error,
    refetch: searchStores
  };
}

// 使用示例
function StoreList() {
  const [coordinates, setCoordinates] = useState({
    longitude: '115.10888',
    latitude: '33.39519'
  });

  const {
    stores,
    currentCity,
    nearestStore,
    loading,
    error,
    refetch
  } = useNearbyStores(coordinates.longitude, coordinates.latitude);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          setCoordinates({
            longitude: position.coords.longitude.toFixed(6),
            latitude: position.coords.latitude.toFixed(6)
          });
        },
        error => alert('无法获取位置: ' + error.message)
      );
    }
  };

  if (loading) return <div>搜索中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div>
      <div>
        <input
          value={coordinates.longitude}
          onChange={e => setCoordinates(prev => ({...prev, longitude: e.target.value}))}
          placeholder="经度"
        />
        <input
          value={coordinates.latitude}
          onChange={e => setCoordinates(prev => ({...prev, latitude: e.target.value}))}
          placeholder="纬度"
        />
        <button onClick={getCurrentLocation}>获取当前位置</button>
        <button onClick={refetch}>重新搜索</button>
      </div>

      {currentCity && (
        <div>
          <h2>📍 {currentCity.city_name}</h2>
          {nearestStore && (
            <p>最近门店: {nearestStore.name} ({nearestStore.distance_text})</p>
          )}
        </div>
      )}

      <div>
        {stores.map(store => (
          <div key={store.id} style={{border: '1px solid #ddd', padding: '15px', margin: '10px 0'}}>
            <h3>{store.name}</h3>
            <p>📍 {store.address}</p>
            <p>📞 {store.phone}</p>
            <p>📏 {store.distance_text}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 🌍 常用城市坐标

| 城市 | 经度 | 纬度 | 快速测试 |
|------|------|------|----------|
| 北京 | 116.407526 | 39.904030 | [测试](http://localhost:8000/api/stores/nearby?longitude=116.407526&latitude=39.904030&city_only=1) |
| 上海 | 121.473701 | 31.230416 | [测试](http://localhost:8000/api/stores/nearby?longitude=121.473701&latitude=31.230416&city_only=1) |
| 广州 | 113.264434 | 23.129162 | [测试](http://localhost:8000/api/stores/nearby?longitude=113.264434&latitude=23.129162&city_only=1) |
| 深圳 | 114.064665 | 22.548447 | [测试](http://localhost:8000/api/stores/nearby?longitude=114.064665&latitude=22.548447&city_only=1) |
| 郑州 | 113.625368 | 34.746599 | [测试](http://localhost:8000/api/stores/nearby?longitude=113.625368&latitude=34.746599&city_only=1) |
| 杭州 | 120.153576 | 30.287459 | [测试](http://localhost:8000/api/stores/nearby?longitude=120.153576&latitude=30.287459&city_only=1) |
| 南京 | 118.767413 | 32.041544 | [测试](http://localhost:8000/api/stores/nearby?longitude=118.767413&latitude=32.041544&city_only=1) |
| 武汉 | 114.298572 | 30.584355 | [测试](http://localhost:8000/api/stores/nearby?longitude=114.298572&latitude=30.584355&city_only=1) |

## 📊 响应数据结构

```javascript
{
    "code": 200,
    "message": "success",
    "data": {
        "location": {
            "longitude": "115.10888",
            "latitude": "33.39519"
        },
        "current_city": {
            "city_code": "410100",
            "city_name": "郑州市"
        },
        "nearest_store": {
            "name": "郑州中原万达店",
            "distance": 2.5,
            "distance_text": "2.5公里",
            "city_code": "410100"
        },
        "stores": [
            {
                "id": 1,
                "store_code": "ZZ001",
                "name": "郑州中原万达店",
                "address": "河南省郑州市中原区中原万达广场",
                "phone": "0371-12345678",
                "longitude": "113.625368",
                "latitude": "34.746599",
                "city_code": "410100",
                "city_name": "郑州市",
                "distance": 2.5,
                "distance_text": "2.5公里",
                "status": "normal",
                "start_time": "09:00",
                "end_time": "22:00"
            }
        ],
        "debug_info": {
            "version": "THINKPHP_V4.0",
            "method": "thinkphp_query_builder",
            "logic_steps": [
                "1. 找到最近门店: 郑州中原万达店",
                "2. 确定目标城市: 郑州市 (410100)",
                "3. 筛选该城市门店: 5个",
                "4. 按距离排序: 完成"
            ],
            "city_code_consistent": true
        }
    }
}
```

## ⚡ 性能优化建议

### 1. 缓存策略
```javascript
// 缓存相同坐标的结果5分钟
const storeCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

function getCachedStores(longitude, latitude) {
    const key = `${longitude},${latitude}`;
    const cached = storeCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return Promise.resolve(cached.data);
    }
    
    return fetchStores(longitude, latitude).then(data => {
        storeCache.set(key, { data, timestamp: Date.now() });
        return data;
    });
}
```

### 2. 防抖处理
```javascript
// 防止频繁请求
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

const debouncedSearch = debounce(searchStores, 500);

// 在输入框变化时使用
document.getElementById('longitude').addEventListener('input', debouncedSearch);
```

### 3. 错误重试
```javascript
async function fetchWithRetry(url, maxRetries = 2) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            const response = await fetch(url, { 
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (i === maxRetries) {
                throw error;
            }
            // 指数退避
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
    }
}
```

## 🔧 调试技巧

### 1. 检查调试信息
```javascript
function analyzeResponse(response) {
    if (response.data.debug_info) {
        const debug = response.data.debug_info;
        
        console.log('🔧 调试信息:');
        console.log('版本:', debug.version);
        console.log('方法:', debug.method);
        console.log('执行步骤:', debug.logic_steps);
        console.log('数据一致性:', debug.city_code_consistent);
        
        if (!debug.city_code_consistent) {
            console.warn('⚠️ 数据一致性检查失败，可能存在跨城市门店');
        }
        
        if (debug.version !== 'THINKPHP_V4.0') {
            console.warn('⚠️ API版本不是最新的');
        }
    }
}
```

### 2. 坐标验证
```javascript
function validateCoordinates(longitude, latitude) {
    const lng = parseFloat(longitude);
    const lat = parseFloat(latitude);
    
    const errors = [];
    
    if (isNaN(lng) || lng < -180 || lng > 180) {
        errors.push('经度必须在-180到180之间');
    }
    
    if (isNaN(lat) || lat < -90 || lat > 90) {
        errors.push('纬度必须在-90到90之间');
    }
    
    if (errors.length > 0) {
        throw new Error(errors.join(', '));
    }
    
    return { longitude: lng, latitude: lat };
}

// 使用示例
try {
    const coords = validateCoordinates('115.10888', '33.39519');
    searchStores(coords.longitude, coords.latitude);
} catch (error) {
    alert('坐标格式错误: ' + error.message);
}
```

## 🎮 在线测试工具

- **交互式测试页面**: [http://localhost:8000/api_examples.html](http://localhost:8000/api_examples.html)
- **完整API文档**: [API_Documentation.md](API_Documentation.md)

## 📞 技术支持

如有问题，请联系开发团队或查看完整文档。
