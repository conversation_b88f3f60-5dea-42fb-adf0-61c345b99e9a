import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 3001, // 改为3001避免与API服务器端口冲突
    proxy: {
      // 代理所有 /api 请求到菜单API服务器
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('🔴 代理错误:', err.message)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('🔗 代理请求:', req.method, req.url, '→', proxyReq.getHeader('host'))
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('📡 代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})
