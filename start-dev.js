#!/usr/bin/env node

/**
 * 开发环境启动脚本
 * 检查API服务器状态并启动前端开发服务器
 */

const { spawn, exec } = require('child_process')
const http = require('http')

// 配置
const API_HOST = 'localhost'
const API_PORT = 8000
const FRONTEND_PORT = 3001

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 检查API服务器状态
function checkApiServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: API_HOST,
      port: API_PORT,
      path: '/api/mobile/menu/categories',
      method: 'GET',
      timeout: 3000
    }, (res) => {
      resolve(res.statusCode === 200)
    })

    req.on('error', () => {
      resolve(false)
    })

    req.on('timeout', () => {
      req.destroy()
      resolve(false)
    })

    req.end()
  })
}

// 检查端口是否被占用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = require('net').createServer()
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(false) // 端口可用
      })
      server.close()
    })
    
    server.on('error', () => {
      resolve(true) // 端口被占用
    })
  })
}

// 启动前端开发服务器
function startFrontend() {
  log('🚀 启动前端开发服务器...', 'blue')
  
  const frontend = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true
  })

  frontend.on('error', (err) => {
    log(`❌ 启动前端服务器失败: ${err.message}`, 'red')
    process.exit(1)
  })

  // 处理退出信号
  process.on('SIGINT', () => {
    log('\n🛑 正在关闭开发服务器...', 'yellow')
    frontend.kill('SIGINT')
    process.exit(0)
  })
}

// 主函数
async function main() {
  log('🔍 检查开发环境...', 'cyan')
  
  // 检查API服务器
  log('📡 检查API服务器状态...', 'blue')
  const apiRunning = await checkApiServer()
  
  if (!apiRunning) {
    log('❌ API服务器未运行或无法访问', 'red')
    log(`   请确保API服务器在 http://${API_HOST}:${API_PORT} 运行`, 'yellow')
    log('   启动API服务器后再运行此脚本', 'yellow')
    
    // 提供启动建议
    log('\n💡 启动建议:', 'cyan')
    log('   1. 检查API服务器是否在端口8000运行', 'yellow')
    log('   2. 确认防火墙设置允许访问端口8000', 'yellow')
    log('   3. 检查API服务器日志是否有错误', 'yellow')
    
    process.exit(1)
  }
  
  log('✅ API服务器运行正常', 'green')
  
  // 检查前端端口
  log('🔍 检查前端端口...', 'blue')
  const frontendPortBusy = await checkPort(FRONTEND_PORT)
  
  if (frontendPortBusy) {
    log(`❌ 端口 ${FRONTEND_PORT} 已被占用`, 'red')
    log('   请关闭占用端口的程序或修改配置', 'yellow')
    process.exit(1)
  }
  
  log(`✅ 端口 ${FRONTEND_PORT} 可用`, 'green')
  
  // 显示环境信息
  log('\n📋 环境信息:', 'cyan')
  log(`   API服务器: http://${API_HOST}:${API_PORT}`, 'blue')
  log(`   前端服务器: http://localhost:${FRONTEND_PORT}`, 'blue')
  log(`   代理配置: /api/* → http://${API_HOST}:${API_PORT}/api/*`, 'blue')
  
  // 显示可用页面
  log('\n🌐 可用页面:', 'cyan')
  log(`   首页: http://localhost:${FRONTEND_PORT}`, 'blue')
  log(`   菜单页面: http://localhost:${FRONTEND_PORT}/menu/1`, 'blue')
  log(`   API测试: http://localhost:${FRONTEND_PORT}/api-test`, 'blue')
  log(`   数据库状态: http://localhost:${FRONTEND_PORT}/database-status`, 'blue')
  
  log('\n🎯 代理状态监控已启用，可在页面中查看连接状态', 'green')
  log('📝 详细配置说明请查看 PROXY_SETUP_README.md', 'yellow')
  
  // 启动前端服务器
  startFrontend()
}

// 错误处理
process.on('uncaughtException', (err) => {
  log(`❌ 未捕获的异常: ${err.message}`, 'red')
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  log(`❌ 未处理的Promise拒绝: ${reason}`, 'red')
  process.exit(1)
})

// 运行主函数
main().catch((err) => {
  log(`❌ 启动失败: ${err.message}`, 'red')
  process.exit(1)
})
