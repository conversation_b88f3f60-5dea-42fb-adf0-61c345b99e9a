import { defineStore } from 'pinia'
import apiService from '../services/api'

export const useLocationStore = defineStore('location', {
  state: () => ({
    currentCity: '北京',
    currentCityCode: '110100', // 添加城市代码
    currentAddress: '北京市朝阳区',
    currentCoordinates: {
      longitude: 116.407526,
      latitude: 39.904030
    },
    nearbyStores: [],
    loading: false,
    error: null,
    allCities: [], // 将通过API动态获取
    citiesLoading: false,
    citiesError: null
  }),

  actions: {
    async setCurrentCity(cityInfo) {
      this.loading = true
      this.error = null

      try {
        // cityInfo 可以是字符串（城市名）或对象（包含name和code）
        const city = typeof cityInfo === 'string' ? { name: cityInfo } : cityInfo



        // 获取城市中心坐标（用于显示）
        const coordinates = await apiService.getCityCoordinates(city.name, city.code)

        // 更新状态
        this.currentCity = city.name
        this.currentCityCode = city.code || this.currentCityCode
        this.currentAddress = `${city.name}市区`
        this.currentCoordinates = coordinates



        // 使用城市信息获取门店（优先使用城市代码）
        const result = await apiService.getStoresByCityInfo(city)

        // 更新门店列表
        this.nearbyStores = result.stores || []

        // 如果API返回了城市代码，更新本地状态
        if (result.current_city && result.current_city.city_code) {
          this.currentCityCode = result.current_city.city_code
        }

      } catch (error) {
        console.error('设置城市失败:', error)
        this.error = error.message
        this.nearbyStores = []
      } finally {
        this.loading = false
      }
    },

    setCurrentAddress(address) {
      this.currentAddress = address
    },

    setCurrentCoordinates(longitude, latitude) {
      this.currentCoordinates = { longitude, latitude }
    },

    async fetchNearbyStores(options = {}) {
      this.loading = true
      this.error = null

      try {
        const { longitude, latitude } = this.currentCoordinates
        const { useCurrentLocation = true } = options

        const result = await apiService.getNearbyStores(longitude, latitude, {
          cityOnly: true,
          limit: 0,
          useCurrentLocation, // 传递是否使用当前位置的标识
          ...options
        })

        // 更新门店列表
        this.nearbyStores = result.stores || []

        // 只有在使用当前位置时才更新城市信息
        if (useCurrentLocation && result.current_city) {
          this.currentCity = result.current_city.city_name
          this.currentAddress = `${result.current_city.city_name}当前位置`
        }

        return result

      } catch (error) {
        console.error('获取附近门店失败:', error)
        this.error = error.message
        this.nearbyStores = []
        throw error
      } finally {
        this.loading = false
      }
    },

    async getCurrentLocation() {
      this.loading = true
      this.error = null

      try {
        const location = await apiService.getCurrentLocation()

        // 更新坐标
        this.setCurrentCoordinates(location.longitude, location.latitude)

        // 获取附近门店来确定城市（使用真实位置）
        const result = await this.fetchNearbyStores({ useCurrentLocation: true })

        return location

      } catch (error) {
        console.error('获取当前位置失败:', error)
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchCitiesList() {
      this.citiesLoading = true
      this.citiesError = null

      try {
        // 优先使用分组城市API，更适合城市选择器
        try {
          const groupedCities = await apiService.getGroupedCities()

          // 将分组数据转换为平铺数组
          const cities = []
          Object.keys(groupedCities).forEach(letter => {
            cities.push(...groupedCities[letter])
          })

          this.allCities = cities
          return cities

        } catch (groupedError) {
          // 降级到普通城市列表API
          const cities = await apiService.getCitiesList()
          this.allCities = cities
          return cities
        }

      } catch (error) {
        console.error('获取城市列表失败:', error)
        this.citiesError = error.message

        // 降级到默认城市列表
        this.allCities = this.getDefaultCities()

        throw error
      } finally {
        this.citiesLoading = false
      }
    },

    getDefaultCities() {
      // 默认城市列表作为降级方案
      return [
        { name: '北京', first_char: 'B', code: '110100' },
        { name: '上海', first_char: 'S', code: '310100' },
        { name: '广州', first_char: 'G', code: '440100' },
        { name: '深圳', first_char: 'S', code: '440300' },
        { name: '杭州', first_char: 'H', code: '330100' },
        { name: '南京', first_char: 'N', code: '320100' },
        { name: '武汉', first_char: 'W', code: '420100' },
        { name: '成都', first_char: 'C', code: '510100' },
        { name: '重庆', first_char: 'C', code: '500100' },
        { name: '天津', first_char: 'T', code: '120100' },
        { name: '西安', first_char: 'X', code: '610100' },
        { name: '沈阳', first_char: 'S', code: '210100' },
        { name: '青岛', first_char: 'Q', code: '370200' },
        { name: '大连', first_char: 'D', code: '210200' },
        { name: '厦门', first_char: 'X', code: '350200' },
        { name: '福州', first_char: 'F', code: '350100' },
        { name: '昆明', first_char: 'K', code: '530100' },
        { name: '长沙', first_char: 'C', code: '430100' },
        { name: '合肥', first_char: 'H', code: '340100' },
        { name: '济南', first_char: 'J', code: '370100' },
        { name: '郑州', first_char: 'Z', code: '410100' }
      ]
    },

    clearError() {
      this.error = null
      this.citiesError = null
    }
  },

  getters: {
    hasStores: (state) => state.nearbyStores.length > 0,
    storeCount: (state) => state.nearbyStores.length,
    isLoading: (state) => state.loading,
    hasError: (state) => !!state.error,
    hasCities: (state) => state.allCities.length > 0,
    isCitiesLoading: (state) => state.citiesLoading,
    hasCitiesError: (state) => !!state.citiesError
  }
})
