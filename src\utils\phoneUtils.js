/**
 * 手机号相关工具函数
 */

/**
 * 隐藏手机号中间4位
 * @param {string} phone - 手机号码
 * @returns {string} 隐藏中间4位的手机号
 */
export function maskPhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return ''
  }
  
  // 移除所有非数字字符
  const cleanPhone = phone.replace(/\D/g, '')
  
  // 检查是否是有效的11位手机号
  if (cleanPhone.length !== 11) {
    return phone // 如果不是11位，返回原始值
  }
  
  // 隐藏中间4位：138****5678
  return cleanPhone.substring(0, 3) + '****' + cleanPhone.substring(7)
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号码
 * @returns {boolean} 是否为有效手机号
 */
export function isValidPhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return false
  }
  
  const cleanPhone = phone.replace(/\D/g, '')
  
  // 中国大陆手机号规则：11位，以1开头
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(cleanPhone)
}

/**
 * 格式化手机号显示（带分隔符）
 * @param {string} phone - 手机号码
 * @param {boolean} mask - 是否隐藏中间4位
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone, mask = false) {
  if (!phone || typeof phone !== 'string') {
    return ''
  }
  
  const cleanPhone = phone.replace(/\D/g, '')
  
  if (cleanPhone.length !== 11) {
    return phone
  }
  
  if (mask) {
    // 138 **** 5678
    return cleanPhone.substring(0, 3) + ' **** ' + cleanPhone.substring(7)
  } else {
    // 138 1234 5678
    return cleanPhone.substring(0, 3) + ' ' + cleanPhone.substring(3, 7) + ' ' + cleanPhone.substring(7)
  }
}

/**
 * 获取手机号运营商信息
 * @param {string} phone - 手机号码
 * @returns {string} 运营商名称
 */
export function getPhoneCarrier(phone) {
  if (!phone || typeof phone !== 'string') {
    return '未知'
  }
  
  const cleanPhone = phone.replace(/\D/g, '')
  
  if (cleanPhone.length !== 11) {
    return '未知'
  }
  
  const prefix = cleanPhone.substring(0, 3)
  
  // 中国移动
  const cmPrefixes = ['134', '135', '136', '137', '138', '139', '147', '150', '151', '152', '157', '158', '159', '178', '182', '183', '184', '187', '188', '198']
  
  // 中国联通
  const cuPrefixes = ['130', '131', '132', '145', '155', '156', '166', '175', '176', '185', '186']
  
  // 中国电信
  const ctPrefixes = ['133', '149', '153', '173', '177', '180', '181', '189', '199']
  
  if (cmPrefixes.includes(prefix)) {
    return '中国移动'
  } else if (cuPrefixes.includes(prefix)) {
    return '中国联通'
  } else if (ctPrefixes.includes(prefix)) {
    return '中国电信'
  } else {
    return '其他'
  }
}
