<template>
  <div class="order-status">
    <!-- 顶部导航 -->
    <div class="header">
      <button class="back-btn" @click="goHome">🏠</button>
      <h1>订单状态</h1>
      <button class="orders-btn" @click="goToOrders">📋</button>
    </div>

    <!-- 订单成功提示 -->
    <div class="success-section">
      <div class="success-icon">✅</div>
      <h2>点餐成功！</h2>
      <p>请耐心等待出餐</p>
    </div>

    <!-- 出餐号显示 -->
    <div class="meal-numbers">
      <h3>您的出餐号</h3>
      <div v-if="orderItems.length === 0" class="no-items">
        <SvgIcon name="food" size="48px" color="#ccc" />
        <p>暂无菜品信息</p>
      </div>
      <div v-else class="numbers-list">
        <div
          v-for="item in orderItems"
          :key="item.id"
          class="meal-number-card"
          :class="`card-${item.status}`"
        >
          <div class="card-left">
            <div class="dish-name">{{ item.name }}</div>
            <div class="dish-phone" v-if="item.phone && item.status === 'ready'">
              <SvgIcon name="phone" size="12px" color="#28a745" />
              {{ maskPhone(item.phone) }}
            </div>
            <div class="status" :class="item.status">
              {{ getStatusText(item.status) }}
            </div>
          </div>
          <div class="card-right">
            <div class="meal-number" v-if="item.mealNumber">{{ item.mealNumber }}</div>
            <div class="meal-number pending" v-else>待分配</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单信息 -->
    <div class="order-info">
      <h3>订单信息</h3>
      <div class="info-row">
        <span class="label">订单号:</span>
        <span class="value">{{ orderNumber }}</span>
      </div>
      <div class="info-row">
        <span class="label">餐厅:</span>
        <span class="value">{{ restaurant.name }}</span>
      </div>

      <div class="info-row">
        <span class="label">下单时间:</span>
        <span class="value">{{ getOrderTimeInfo().display }}</span>
      </div>
      <div class="info-row">
        <span class="label">菜品数量:</span>
        <span class="value total-amount">{{ totalAmount }} 道菜品</span>
      </div>
    </div>

    <!-- 等待提示 -->
    <div class="waiting-tips">
      <h3>温馨提示</h3>
      <ul>
        <li>请留意大屏幕或听取广播叫号</li>
        <li>每道菜品都有独立的出餐号</li>
        <li>出餐时间可能因菜品复杂度而不同</li>
        <li>如有疑问请联系服务员</li>
      </ul>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <button class="action-btn secondary" @click="goToMenu">继续点餐</button>
      <button class="action-btn primary" @click="goToOrders">查看所有订单</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'
import { maskPhone } from '@/utils/phoneUtils'
import { getSmartTimeDisplay } from '@/utils/dateUtils'
import SvgIcon from '@/components/SvgIcon.vue'

export default {
  name: 'OrderStatus',
  components: {
    SvgIcon
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const ordersStore = useOrdersStore()

    const orderNumber = route.params.orderNumber
    const restaurant = ref({})
    const orderItems = ref([])
    const totalAmount = ref(0)
    const orderTime = ref('')
    
    let statusUpdateInterval = null

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '制作中',
        'cooking': '制作中',
        'ready': '已出餐',
        'completed': '已取餐'
      }
      return statusMap[status] || '制作中'
    }

    const goHome = () => {
      router.push('/')
    }

    const goToOrders = () => {
      router.push('/orders')
    }

    const goToMenu = () => {
      router.push(`/menu/${restaurant.value.id}`)
    }

    // 时间相关函数
    const getOrderTimeInfo = () => {
      if (!orderTime.value) return { display: '--' }

      return {
        display: getSmartTimeDisplay(orderTime.value),
        full: orderTime.value
      }
    }

    const generateMealNumbers = (items) => {
      return items.map((item, index) => ({
        ...item,
        mealNumber: String(Math.floor(Math.random() * 900) + 100), // 生成3位数出餐号
        status: 'cooking'
      }))
    }

    const updateOrderStatus = () => {
      // 模拟订单状态更新
      orderItems.value.forEach(item => {
        if (item.status === 'cooking' && Math.random() < 0.1) {
          item.status = 'ready'
        }
      })
    }

    onMounted(async () => {
      // 先初始化订单管理
      ordersStore.init()

      // 从订单管理中获取订单数据
      let order = ordersStore.getOrder(orderNumber)

      // 如果本地没有订单，尝试从API获取
      if (!order && orderNumber.startsWith('ORD')) {
        try {
          const apiOrderId = orderNumber.replace('ORD', '')
          console.log(`🔍 从API获取订单详情: ${apiOrderId}`)
          order = await ordersStore.fetchOrderDetail(apiOrderId)
        } catch (error) {
          console.error('❌ 获取订单详情失败:', error)
        }
      }

      if (order) {
        restaurant.value = order.restaurant
        orderItems.value = order.items
        totalAmount.value = order.totalAmount
        orderTime.value = new Date(order.orderTime).toLocaleString()

        console.log('📋 订单信息:', order)

        // 如果有API订单ID，开始轮询状态
        if (order.apiOrderId) {
          ordersStore.startPollingOrder(order.apiOrderId)
        }
      } else {
        // 如果找不到订单，可能是直接访问的URL，使用历史状态
        console.warn('⚠️ 未找到订单，使用历史状态:', orderNumber)
        const orderData = history.state || {}
        restaurant.value = orderData.restaurant || { name: '美味餐厅' }
        totalAmount.value = orderData.totalAmount || 0
        orderTime.value = new Date().toLocaleString()

        const items = orderData.orderItems || []
        orderItems.value = generateMealNumbers(items)
      }

      // 监听订单状态变化
      statusUpdateInterval = setInterval(() => {
        const currentOrder = ordersStore.getOrder(orderNumber)
        if (currentOrder) {
          orderItems.value = [...currentOrder.items]
          console.log('🔄 订单状态更新:', currentOrder.items.map(item => ({
            name: item.name,
            status: item.status,
            mealNumber: item.mealNumber
          })))
        }
      }, 5000)
    })

    onUnmounted(() => {
      if (statusUpdateInterval) {
        clearInterval(statusUpdateInterval)
      }
    })

    return {
      orderNumber,
      restaurant,
      orderItems,
      totalAmount,
      orderTime,
      getStatusText,
      getOrderTimeInfo,
      goHome,
      goToOrders,
      goToMenu,
      maskPhone
    }
  }
}
</script>

<style scoped>
.order-status {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn, .orders-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.success-section {
  text-align: center;
  padding: 40px 20px;
  background: white;
  margin-bottom: 8px;
}

.success-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.success-section h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #4caf50;
}

.success-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.meal-numbers {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.meal-numbers h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.no-items {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-items-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.numbers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-number-card {
  background: white;
  border: 2px solid #007AFF;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
  min-height: 80px;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .meal-number-card {
    padding: 12px;
    min-height: 70px;
  }

  .meal-number-card .dish-name {
    font-size: 14px;
  }

  .meal-number-card .meal-number {
    font-size: 24px;
    padding: 6px 12px;
  }

  .card-right {
    min-width: 70px;
  }
}

.meal-number-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
}

.card-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meal-number-card .dish-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dish-phone {
  font-size: 13px;
  color: #28a745;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  margin: 4px 0;
  background: #f8fff9;
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid #28a745;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.card-right {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.meal-number-card .meal-number {
  font-size: 28px;
  font-weight: bold;
  color: #007AFF;
  background: #f0f8ff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #007AFF;
}

.meal-number-card .meal-number.pending {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  border: 1px solid #ddd;
}

.meal-number-card .status {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
  display: inline-block;
}

.status.cooking {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status.ready {
  background: #d4edda;
  color: #155724;
  border: 1px solid #00b894;
  animation: pulse 1s infinite;
}

.status.completed {
  background: #e2e3e5;
  color: #6c757d;
  border: 1px solid #ced4da;
}

/* 根据状态改变卡片样式 */
.meal-number-card.card-cooking {
  border-color: #007AFF;
  background: white;
}

.meal-number-card.card-ready {
  border-color: #28a745;
  background: #f8fff9;
  animation: cardPulse 2s infinite;
}

.meal-number-card.card-completed {
  border-color: #6c757d;
  background: #f8f9fa;
  opacity: 0.8;
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
  }
  50% {
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.4);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.order-info {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.order-info h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  font-size: 14px;
  font-weight: 500;
}

.total-amount {
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.waiting-tips {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}

.waiting-tips h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.waiting-tips ul {
  margin: 0;
  padding-left: 20px;
}

.waiting-tips li {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
  border: none;
}

.action-btn:hover {
  transform: translateY(-1px);
}
</style>
