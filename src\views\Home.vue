<template>
  <div class="home">
    <!-- 顶部位置栏 -->
    <header class="location-header">
      <div class="location-info" @click="goToCitySelector">
        <div class="city-name">{{ currentCity }}</div>
        <div class="address">{{ currentAddress }}</div>
      </div>
      <button class="change-city-btn" @click="goToCitySelector">
        切换城市
      </button>
    </header>

    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-box">
        <SvgIcon name="search" size="16px" color="#999" />
        <input
          type="text"
          placeholder="搜索餐厅、菜品"
          class="search-input"
          v-model="searchKeyword"
          @input="onSearchInput"
          @keyup.enter="onSearch"
        >
      </div>
    </div>





    <!-- 餐厅列表 -->
    <div class="restaurant-section">
      <div class="section-header">
        <h3 class="section-title">附近餐厅</h3>
        <div class="header-buttons">
          <button class="location-btn" @click="getCurrentLocation" :disabled="locationStore.isLoading">
            {{ locationStore.isLoading ? '定位中...' : '📍 重新定位' }}
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="locationStore.isLoading" class="loading-state">
        <div class="loading-spinner">🔄</div>
        <p>正在获取附近餐厅...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="locationStore.hasError" class="error-state">
        <p>{{ locationStore.error }}</p>
        <button @click="retryFetch" class="retry-btn">重试</button>
      </div>

      <!-- 餐厅列表 -->
      <div v-else-if="locationStore.hasStores" class="restaurant-list">
        <div class="restaurant-item" v-for="restaurant in locationStore.nearbyStores" :key="restaurant.id" @click="goToRestaurant(restaurant)">
          <div class="restaurant-image">
            <SvgIcon name="restaurant" size="32px" color="#007AFF" />
          </div>
          <div class="restaurant-info">
            <h4 class="restaurant-name">{{ restaurant.name }}</h4>
            <div class="restaurant-address">{{ restaurant.address }}</div>
            <div class="restaurant-meta">
              <span class="hours">
                <SvgIcon name="clock" size="14px" color="#666" />
                {{ restaurant.start_time }}-{{ restaurant.end_time }}
              </span>
              <span class="status" :class="getRestaurantBusinessStatus(restaurant).className">
                {{ getRestaurantBusinessStatus(restaurant).text }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无餐厅状态 -->
      <div v-else class="no-stores-state">
        <SvgIcon name="restaurant" size="64px" color="#ccc" />
        <p>附近暂无餐厅</p>
        <button @click="getCurrentLocation" class="retry-btn">重新定位</button>
      </div>
    </div>

    <!-- 底部占位 -->
    <div class="bottom-spacer"></div>
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useLocationStore } from '../stores/location'
import { getBusinessStatus } from '@/utils/businessHours'
import SvgIcon from '@/components/SvgIcon.vue'
import apiService from '@/services/api'

export default {
  name: 'Home',
  components: {
    SvgIcon
  },
  setup() {
    const router = useRouter()
    const locationStore = useLocationStore()

    const currentCity = computed(() => locationStore.currentCity)
    const currentAddress = computed(() => locationStore.currentAddress)
    const searchKeyword = ref('')
    let searchTimeout = null

    // 搜索相关方法
    const onSearchInput = () => {
      // 防抖处理，避免频繁请求
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        if (searchKeyword.value.trim()) {
          performSearch()
        }
      }, 500)
    }

    const onSearch = () => {
      if (searchKeyword.value.trim()) {
        performSearch()
      }
    }

    const performSearch = async () => {
      try {
        const searchResults = await apiService.searchDishes(searchKeyword.value.trim())
        if (searchResults && searchResults.list && searchResults.list.length > 0) {
          // 跳转到搜索结果页面或显示搜索结果
          // 这里可以创建一个搜索结果页面，或者在当前页面显示结果
          console.log('搜索结果:', searchResults)
          alert(`找到 ${searchResults.list.length} 个相关菜品`)
        } else {
          alert('未找到相关菜品')
        }
      } catch (error) {
        console.error('搜索失败:', error)
        alert('搜索失败，请稍后重试')
      }
    }





    const goToCitySelector = () => {
      router.push('/city')
    }

    const goToRestaurant = (restaurant) => {
      router.push({
        path: `/menu/${restaurant.id}`,
        state: { restaurant }
      })
    }

    const getCurrentLocation = async () => {
      try {
        await locationStore.getCurrentLocation()
      } catch (error) {
        alert('获取位置失败: ' + error.message)
      }
    }

    const getRestaurantBusinessStatus = (restaurant) => {
      return getBusinessStatus(restaurant.start_time, restaurant.end_time)
    }

    const retryFetch = async () => {
      try {
        locationStore.clearError()
        await locationStore.fetchNearbyStores()
      } catch (error) {
        // 错误已经在store中处理
      }
    }



    // 组件挂载时获取当前城市的门店
    onMounted(async () => {
      try {
        // 使用当前城市信息获取门店，包含城市代码
        const cityInfo = {
          name: locationStore.currentCity,
          code: locationStore.currentCityCode
        }
        await locationStore.setCurrentCity(cityInfo)
      } catch (error) {
        console.error('初始化获取门店失败:', error)
      }
    })

    return {
      currentCity,
      currentAddress,
      locationStore,
      searchKeyword,
      goToCitySelector,
      goToRestaurant,
      getCurrentLocation,
      retryFetch,
      getRestaurantBusinessStatus,
      onSearchInput,
      onSearch,
      performSearch
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部位置栏 */
.location-header {
  background: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.location-info {
  cursor: pointer;
}

.city-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.address {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.change-city-btn {
  background: #007AFF;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  transition: opacity 0.2s;
}

.change-city-btn:hover {
  opacity: 0.8;
}

/* 搜索栏 */
.search-section {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-box {
  background: #f5f5f5;
  border-radius: 20px;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.search-box:focus-within {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
  border: 1px solid #007AFF;
}

.search-box:focus-within .svg-icon {
  color: #007AFF !important;
}



.search-input {
  flex: 1;
  background: transparent;
  font-size: 16px;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}





/* 商家列表 */
.restaurant-section {
  background: white;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.location-btn {
  background: #f0f0f0;
  color: #666;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.location-btn:hover:not(:disabled) {
  background: #e0e0e0;
}

.location-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}



.restaurant-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.restaurant-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s;
  cursor: pointer;
}

.restaurant-item:hover {
  background: #f8f8f8;
}

.restaurant-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}



.restaurant-info {
  flex: 1;
}

.restaurant-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.restaurant-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.restaurant-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.distance {
  color: #ff6b35;
  font-weight: 500;
}

.phone, .hours {
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 40px 20px;
  color: #ff6b35;
}

.retry-btn {
  background: #007AFF;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 12px;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 无商家状态 */
.no-stores-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-stores-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.bottom-spacer {
  height: 60px;
}
</style>
