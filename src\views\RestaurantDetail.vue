<template>
  <div class="restaurant-detail">
    <!-- 顶部导航栏 -->
    <header class="detail-header">
      <button class="back-btn" @click="goBack">
        ← 返回
      </button>
      <h1 class="page-title">商家详情</h1>
      <div class="header-spacer"></div>
    </header>

    <!-- 商家信息 -->
    <div class="restaurant-info-section" v-if="restaurant">
      <div class="restaurant-banner">
        <div class="restaurant-image">
          <div class="image-placeholder">🏪</div>
        </div>
        <div class="restaurant-basic-info">
          <h2 class="restaurant-name">{{ restaurant.name }}</h2>
          <div class="restaurant-tags">
            <span class="tag" v-for="tag in restaurant.tags" :key="tag">{{ tag }}</span>
          </div>
          <div class="restaurant-meta">
            <span class="rating">⭐ {{ restaurant.rating }}</span>
            <span class="delivery-time">{{ restaurant.deliveryTime }}分钟</span>
            <span class="delivery-fee">配送费¥{{ restaurant.deliveryFee }}</span>
          </div>
        </div>
      </div>

      <!-- 商家详细信息 -->
      <div class="restaurant-details">
        <div class="detail-item">
          <span class="detail-label">营业时间:</span>
          <span class="detail-value">{{ restaurant.businessHours }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">商家地址:</span>
          <span class="detail-value">{{ restaurant.address }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">联系电话:</span>
          <span class="detail-value">{{ restaurant.phone }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">起送金额:</span>
          <span class="detail-value">¥{{ restaurant.minOrder }}</span>
        </div>
      </div>
    </div>

    <!-- 菜单分类 -->
    <div class="menu-section">
      <div class="menu-categories">
        <div 
          class="category-tab" 
          v-for="category in menuCategories" 
          :key="category.id"
          :class="{ active: activeCategory === category.id }"
          @click="setActiveCategory(category.id)"
        >
          {{ category.name }}
        </div>
      </div>

      <!-- 菜品列表 -->
      <div class="menu-items">
        <div class="menu-category" v-for="category in menuCategories" :key="category.id" v-show="activeCategory === category.id">
          <h3 class="category-title">{{ category.name }}</h3>
          <div class="items-list">
            <div class="menu-item" v-for="item in category.items" :key="item.id">
              <div class="item-image">
                <div class="image-placeholder">🍽️</div>
              </div>
              <div class="item-info">
                <h4 class="item-name">{{ item.name }}</h4>
                <p class="item-description">{{ item.description }}</p>
                <div class="item-meta">
                  <span class="item-sales">月售{{ item.sales }}份</span>
                </div>
              </div>
              <div class="item-actions">
                <button class="add-btn" @click="addToCart(item)">+</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 购物车悬浮按钮 -->
    <div class="cart-float" v-if="cartCount > 0">
      <div class="cart-info">
        <span class="cart-count">{{ cartCount }}</span>
        <span class="cart-total">¥{{ cartTotal }}</span>
      </div>
      <button class="checkout-btn">去结算</button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import apiService from '../services/api'

export default {
  name: 'RestaurantDetail',
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    const restaurant = ref(null)
    const activeCategory = ref(1)
    const cart = ref([])

    // 模拟商家数据
    const restaurantData = {
      1: {
        id: 1,
        name: '麦当劳(王府井店)',
        tags: ['汉堡', '快餐', '儿童套餐'],
        rating: 4.8,
        deliveryTime: 25,
        deliveryFee: 3,
        businessHours: '06:00-24:00',
        address: '北京市东城区王府井大街138号',
        phone: '010-12345678',
        minOrder: 20
      },
      2: {
        id: 2,
        name: '肯德基(三里屯店)',
        tags: ['炸鸡', '汉堡', '早餐'],
        rating: 4.7,
        deliveryTime: 30,
        deliveryFee: 4,
        businessHours: '06:30-23:30',
        address: '北京市朝阳区三里屯路19号',
        phone: '010-87654321',
        minOrder: 25
      },
      3: {
        id: 3,
        name: '星巴克(国贸店)',
        tags: ['咖啡', '轻食', '甜品'],
        rating: 4.9,
        deliveryTime: 20,
        deliveryFee: 5,
        businessHours: '07:00-22:00',
        address: '北京市朝阳区建国门外大街1号',
        phone: '010-11223344',
        minOrder: 30
      },
      4: {
        id: 4,
        name: '海底捞火锅',
        tags: ['火锅', '川菜', '服务好'],
        rating: 4.8,
        deliveryTime: 45,
        deliveryFee: 6,
        businessHours: '11:00-02:00',
        address: '北京市朝阳区望京SOHO T1',
        phone: '010-99887766',
        minOrder: 50
      },
      5: {
        id: 5,
        name: '黄焖鸡米饭',
        tags: ['中式快餐', '米饭', '实惠'],
        rating: 4.5,
        deliveryTime: 35,
        deliveryFee: 2,
        businessHours: '10:00-22:00',
        address: '北京市海淀区中关村大街27号',
        phone: '010-55443322',
        minOrder: 15
      }
    }

    const menuCategories = ref([
      {
        id: 1,
        name: '热销推荐',
        items: [
          { id: 1, name: '巨无霸套餐', description: '经典巨无霸汉堡+薯条+可乐', sales: 1200 },
          { id: 2, name: '麦辣鸡腿堡套餐', description: '麦辣鸡腿堡+薯条+雪碧', sales: 980 },
          { id: 3, name: '双层吉士汉堡', description: '双层牛肉饼+芝士+蔬菜', sales: 756 }
        ]
      },
      {
        id: 2,
        name: '汉堡主食',
        items: [
          { id: 4, name: '巨无霸', description: '经典双层牛肉汉堡', sales: 2100 },
          { id: 5, name: '麦辣鸡腿堡', description: '香辣鸡腿肉+生菜+沙拉酱', sales: 1800 },
          { id: 6, name: '板烧鸡腿堡', description: '嫩滑鸡腿肉+蔬菜', sales: 1500 }
        ]
      },
      {
        id: 3,
        name: '小食饮品',
        items: [
          { id: 7, name: '薯条(大)', description: '金黄酥脆薯条', sales: 3200 },
          { id: 8, name: '可乐(中杯)', description: '冰爽可口可乐', sales: 2800 },
          { id: 9, name: '苹果派', description: '香甜苹果馅酥脆外皮', sales: 890 }
        ]
      }
    ])

    const cartCount = computed(() => {
      return cart.value.reduce((total, item) => total + item.quantity, 0)
    })

    const cartTotal = computed(() => {
      return 0 // 不再计算价格
    })

    const goBack = () => {
      router.back()
    }

    const setActiveCategory = (categoryId) => {
      activeCategory.value = categoryId
    }

    const addToCart = (item) => {
      const existingItem = cart.value.find(cartItem => cartItem.id === item.id)
      if (existingItem) {
        existingItem.quantity++
      } else {
        cart.value.push({ ...item, quantity: 1 })
      }
    }

    onMounted(async () => {
      const restaurantId = parseInt(route.params.id)

      try {
        // 尝试从API获取门店详情
        const storeDetail = await apiService.getStoreDetail(restaurantId)
        if (storeDetail) {
          // 将API数据转换为组件需要的格式
          restaurant.value = {
            id: storeDetail.id,
            name: storeDetail.name,
            tags: ['门店'], // API中可能没有tags字段，使用默认值
            rating: 4.8, // API中可能没有rating字段，使用默认值
            deliveryTime: 30, // API中可能没有deliveryTime字段，使用默认值
            deliveryFee: 5, // API中可能没有deliveryFee字段，使用默认值
            businessHours: `${storeDetail.start_time || '09:00'}-${storeDetail.end_time || '22:00'}`,
            address: storeDetail.address,
            phone: storeDetail.phone,
            minOrder: 20 // 默认起送金额
          }
        } else {
          // 如果API没有返回数据，使用模拟数据
          restaurant.value = restaurantData[restaurantId]
        }
      } catch (error) {
        console.error('获取门店详情失败:', error)
        // 降级到模拟数据
        restaurant.value = restaurantData[restaurantId]
      }

      if (!restaurant.value) {
        router.push('/')
      }
    })

    return {
      restaurant,
      menuCategories,
      activeCategory,
      cart,
      cartCount,
      cartTotal,
      goBack,
      setActiveCategory,
      addToCart
    }
  }
}
</script>

<style scoped>
.restaurant-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.detail-header {
  background: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  color: #007AFF;
  font-size: 16px;
  cursor: pointer;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-spacer {
  width: 60px;
}

/* 商家信息 */
.restaurant-info-section {
  background: white;
  margin-bottom: 12px;
}

.restaurant-banner {
  padding: 16px;
  display: flex;
  gap: 12px;
}

.restaurant-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.image-placeholder {
  font-size: 32px;
}

.restaurant-basic-info {
  flex: 1;
}

.restaurant-name {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.restaurant-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.restaurant-meta {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #666;
}

.rating {
  color: #ff6b35;
}

.restaurant-details {
  padding: 0 16px 16px;
  border-top: 1px solid #f0f0f0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #666;
  font-size: 14px;
}

.detail-value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 菜单部分 */
.menu-section {
  background: white;
}

.menu-categories {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
}

.category-tab {
  padding: 16px 20px;
  font-size: 16px;
  color: #666;
  cursor: pointer;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.category-tab.active {
  color: #007AFF;
  border-bottom-color: #007AFF;
}

.menu-items {
  padding: 16px;
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.menu-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s;
}

.menu-item:hover {
  background: #f8f8f8;
}

.menu-item .item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.item-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
}

.item-meta {
  display: flex;
  gap: 12px;
  font-size: 14px;
}



.item-sales {
  color: #999;
}

.item-actions {
  display: flex;
  align-items: center;
}

.add-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007AFF;
  color: white;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
}

.add-btn:hover {
  background: #0056b3;
}

/* 购物车悬浮按钮 */
.cart-float {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  background: #333;
  border-radius: 25px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
}

.cart-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.cart-count {
  background: #ff6b35;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.cart-total {
  font-size: 16px;
  font-weight: 600;
}

.checkout-btn {
  background: #007AFF;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.checkout-btn:hover {
  background: #0056b3;
}
</style>
