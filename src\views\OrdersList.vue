<template>
  <div class="orders-list">
    <!-- 顶部导航 -->
    <div class="header">
      <button class="back-btn" @click="goBack">←</button>
      <h1>我的订单</h1>
    </div>

    <!-- 订单列表 -->
    <div class="orders-content">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">🔄</div>
        <p>正在加载订单...</p>
      </div>

      <div v-else-if="orders.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <p>暂无订单记录</p>
        <button class="go-order-btn" @click="goHome">去点餐</button>
      </div>

      <div v-else class="orders-container">
        <div 
          v-for="order in orders" 
          :key="order.orderNumber"
          class="order-card"
          @click="goToOrderDetail(order)"
        >
          <div class="order-header">
            <div class="restaurant-name">{{ order.restaurant.name }}</div>
            <div class="order-status" :class="order.overallStatus">
              {{ getOverallStatusText(order.overallStatus) }}
            </div>
          </div>

          <div class="order-items">
            <div 
              v-for="item in order.items.slice(0, 3)" 
              :key="item.id"
              class="order-item"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="meal-number">{{ item.mealNumber }}</span>
              <span class="item-status" :class="item.status">
                {{ getStatusText(item.status) }}
              </span>
            </div>
            <div v-if="order.items.length > 3" class="more-items">
              还有{{ order.items.length - 3 }}道菜品...
            </div>
          </div>

          <div class="order-footer">
            <div class="order-info">
              <span class="order-time">{{ formatTime(order.orderTime) }}</span>
            </div>
            <div class="order-total">{{ order.totalAmount }} 道菜品</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrdersStore } from '@/stores/orders'
import { getSmartTimeDisplay } from '@/utils/dateUtils'

export default {
  name: 'OrdersList',
  setup() {
    const router = useRouter()
    const ordersStore = useOrdersStore()
    const loading = ref(true)

    const orders = computed(() => ordersStore.allOrders)

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '制作中',
        'cooking': '制作中',
        'ready': '已出餐',
        'completed': '已取餐'
      }
      return statusMap[status] || '制作中'
    }

    const getOverallStatusText = (status) => {
      const statusMap = {
        'cooking': '制作中',
        'partial': '部分出餐',
        'ready': '全部出餐',
        'completed': '已完成'
      }
      return statusMap[status] || '制作中'
    }

    const formatTime = (timeString) => {
      return getSmartTimeDisplay(timeString)
    }

    const goBack = () => {
      router.go(-1)
    }

    const goHome = () => {
      router.push('/')
    }

    const goToOrderDetail = (order) => {
      router.push({
        name: 'OrderStatus',
        params: { orderNumber: order.orderNumber }
      })
    }

    const loadOrders = async () => {
      loading.value = true

      try {
        // 初始化订单管理并从本地存储加载
        ordersStore.init()

        // 如果有用户手机号，从API获取最新订单
        const customerPhone = ordersStore.getCustomerPhone()
        if (customerPhone) {
          console.log('📋 从API获取用户订单列表...', customerPhone)
          await ordersStore.fetchUserOrders()
        } else {
          console.log('ℹ️ 未设置用户手机号，显示本地订单')
        }

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        console.error('❌ 加载订单失败:', error)
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadOrders()
    })

    return {
      loading,
      orders,
      getStatusText,
      getOverallStatusText,
      formatTime,
      goBack,
      goHome,
      goToOrderDetail
    }
  }
}
</script>

<style scoped>
.orders-list {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  margin-right: 12px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.orders-content {
  padding: 16px;
}

.loading-state, .empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.loading-spinner {
  font-size: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.go-order-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 16px;
}

.orders-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #eee;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.restaurant-name {
  font-size: 16px;
  font-weight: 600;
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.order-status.cooking {
  background: #fff3cd;
  color: #856404;
}

.order-status.partial {
  background: #cce5ff;
  color: #0066cc;
}

.order-status.ready {
  background: #d4edda;
  color: #155724;
}

.order-status.completed {
  background: #e2e3e5;
  color: #6c757d;
}

.order-items {
  margin-bottom: 12px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 14px;
}

.item-name {
  flex: 1;
}

.meal-number {
  color: #007AFF;
  font-weight: 600;
  margin: 0 12px;
}

.item-status {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

.item-status.cooking {
  background: #fff3cd;
  color: #856404;
}

.item-status.ready {
  background: #d4edda;
  color: #155724;
}

.item-status.completed {
  background: #e2e3e5;
  color: #6c757d;
}

.more-items {
  color: #666;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-time {
  color: #666;
  font-size: 12px;
}

.order-total {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
</style>
